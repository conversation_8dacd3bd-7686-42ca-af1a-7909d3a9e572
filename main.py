#!/usr/bin/env python3
"""
AI Music Organization Assistant - Main Application Entry Point

This is the main entry point for the AI Music Organization Assistant application.
It provides both GUI and command-line interfaces for the music organization system.

Usage:
    python main.py              # Start GUI application
    python main.py --cli        # Start command-line interface
    python main.py --scan DIR   # Scan directory from command line
    python main.py --help       # Show help

Author: AI Music Organization Assistant Project
"""

import sys
import argparse
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src import MainWindow, MusicScanner, MetadataExtractor, DatabaseManager, get_config


def run_gui():
    """Run the GUI application."""
    print("Starting AI Music Organization Assistant GUI...")
    app = MainWindow()
    app.run()


def run_cli_scan(directory: str):
    """Run a command-line scan of a directory."""
    print(f"Scanning directory: {directory}")
    
    # Initialize components
    scanner = MusicScanner()
    extractor = MetadataExtractor()
    database = DatabaseManager()
    
    # Initialize database
    if not database.initialize_database():
        print("Error: Failed to initialize database")
        return 1
    
    # Progress callback
    def progress_callback(current, total, current_file):
        if current % 100 == 0 or current == total:
            percentage = (current / total) * 100 if total > 0 else 0
            print(f"Progress: {current}/{total} ({percentage:.1f}%) - {Path(current_file).name}")
    
    try:
        # Scan directory
        print("Scanning for audio files...")
        result = scanner.scan_directory(directory, progress_callback)
        
        print(f"\nScan completed:")
        print(f"  - Total files scanned: {result.total_files}")
        print(f"  - Audio files found: {len(result.audio_files)}")
        print(f"  - Scan time: {result.scan_time:.2f} seconds")
        print(f"  - Errors: {len(result.errors)}")
        
        if result.errors:
            print("\nErrors encountered:")
            for error in result.errors[:5]:  # Show first 5 errors
                print(f"  - {error}")
            if len(result.errors) > 5:
                print(f"  ... and {len(result.errors) - 5} more errors")
        
        # Extract metadata and store in database
        if result.audio_files:
            print(f"\nExtracting metadata from {len(result.audio_files)} audio files...")
            
            processed = 0
            successful = 0
            
            for audio_file in result.audio_files:
                processed += 1
                
                if processed % 50 == 0 or processed == len(result.audio_files):
                    print(f"Processing metadata: {processed}/{len(result.audio_files)}")
                
                # Extract metadata
                metadata = extractor.extract_metadata(audio_file)
                if metadata:
                    # Store in database
                    song_id = database.insert_song(metadata)
                    if song_id:
                        successful += 1
            
            print(f"\nMetadata extraction completed:")
            print(f"  - Files processed: {processed}")
            print(f"  - Successfully stored: {successful}")
            print(f"  - Failed: {processed - successful}")
        
        # Show final statistics
        db_stats = database.get_database_stats()
        print(f"\nDatabase statistics:")
        print(f"  - Total songs: {db_stats.get('total_songs', 0)}")
        print(f"  - Database size: {db_stats.get('database_size', 0):,} bytes")
        
        return 0
        
    except Exception as e:
        print(f"Error during scan: {e}")
        return 1
    
    finally:
        database.close_connections()


def run_cli():
    """Run the command-line interface."""
    print("AI Music Organization Assistant - Command Line Interface")
    print("=" * 60)
    
    config = get_config()
    print(f"Project: {config['project']['name']} v{config['project']['version']}")
    print(f"Supported formats: {', '.join(sorted(config['audio_formats']))}")
    
    while True:
        print("\nAvailable commands:")
        print("  1. Scan directory")
        print("  2. Show statistics")
        print("  3. Exit")
        
        choice = input("\nEnter your choice (1-3): ").strip()
        
        if choice == "1":
            directory = input("Enter directory path to scan: ").strip()
            if directory and Path(directory).exists():
                run_cli_scan(directory)
            else:
                print("Invalid directory path")
        
        elif choice == "2":
            # Show statistics
            database = DatabaseManager()
            database.initialize_database()
            stats = database.get_database_stats()
            
            print("\nCurrent Statistics:")
            print(f"  - Total songs in database: {stats.get('total_songs', 0)}")
            print(f"  - Total patterns: {stats.get('total_patterns', 0)}")
            print(f"  - Total log entries: {stats.get('total_log_entries', 0)}")
            print(f"  - Database size: {stats.get('database_size', 0):,} bytes")
            
            database.close_connections()
        
        elif choice == "3":
            print("Goodbye!")
            break
        
        else:
            print("Invalid choice. Please enter 1, 2, or 3.")


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="AI Music Organization Assistant",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py                    # Start GUI
  python main.py --cli              # Start CLI
  python main.py --scan /music      # Scan directory
        """
    )
    
    parser.add_argument(
        "--cli", 
        action="store_true", 
        help="Start command-line interface"
    )
    
    parser.add_argument(
        "--scan", 
        metavar="DIRECTORY", 
        help="Scan specified directory from command line"
    )
    
    parser.add_argument(
        "--version", 
        action="version", 
        version="AI Music Organization Assistant v0.1.0"
    )
    
    args = parser.parse_args()
    
    try:
        if args.scan:
            # Command-line scan
            if not Path(args.scan).exists():
                print(f"Error: Directory '{args.scan}' does not exist")
                return 1
            return run_cli_scan(args.scan)
        
        elif args.cli:
            # Command-line interface
            run_cli()
            return 0
        
        else:
            # GUI application (default)
            run_gui()
            return 0
    
    except KeyboardInterrupt:
        print("\nOperation cancelled by user")
        return 1
    
    except Exception as e:
        print(f"Unexpected error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
