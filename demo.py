#!/usr/bin/env python3
"""
AI Music Organization Assistant - Interactive Demo

This script demonstrates the AI capabilities we've built in Sprints 1 and 2.
It shows the complete pipeline from scanning to intelligent analysis.

Author: AI Music Organization Assistant Project
"""

import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src import (
    MusicScanner, MetadataExtractor, DatabaseManager,
    AudioAnalyzer, PatternRecognizer, CommentAnalyzer, PatternLearner
)
from src.ai_organizer import AIOrganizer


def print_header(title):
    """Print a formatted header."""
    print("\n" + "=" * 60)
    print(f"  {title}")
    print("=" * 60)


def print_section(title):
    """Print a formatted section header."""
    print(f"\n🔹 {title}")
    print("-" * 40)


def demo_core_components():
    """Demonstrate the core components."""
    print_header("AI MUSIC ORGANIZATION ASSISTANT - DEMO")
    
    print("Welcome to the AI Music Organization Assistant!")
    print("This demo showcases the intelligent music organization system we've built.")
    print("\nProject Status:")
    print("✅ Sprint 1: File System Foundation - COMPLETE")
    print("✅ Sprint 2: Pattern Recognition Intelligence - COMPLETE")
    print("🚧 Sprint 3: Intelligent Organization Engine - READY TO START")
    
    # Component 1: Music Scanner
    print_section("1. Music Scanner - File Discovery Engine")
    scanner = MusicScanner()
    print(f"   📁 Supported formats: {len(scanner.supported_formats)} audio types")
    print(f"   🔍 Max scan depth: {scanner.max_depth} levels")
    print(f"   📊 Scanner stats: {scanner.get_stats()}")
    
    # Component 2: Metadata Extractor
    print_section("2. Metadata Extractor - Audio Information Engine")
    extractor = MetadataExtractor()
    print(f"   🎵 Supported formats: MP3, FLAC, WAV, M4A, OGG, AAC, WMA")
    print(f"   🏷️  Metadata fields: 20+ fields per track")
    print(f"   📊 Extractor stats: {extractor.get_stats()}")
    
    # Component 3: Database Manager
    print_section("3. Database Manager - Intelligent Storage")
    database = DatabaseManager()
    database.initialize_database()
    db_stats = database.get_database_stats()
    print(f"   💾 Database initialized successfully")
    print(f"   📈 Current stats: {db_stats['total_songs']} songs, {db_stats['total_patterns']} patterns")
    print(f"   🔒 Features: SQLite, parameterized queries, connection pooling")
    database.close_connections()
    
    # Component 4: Audio Analyzer (Sprint 2)
    print_section("4. Audio Analyzer - AI Listening Engine")
    analyzer = AudioAnalyzer()
    print(f"   🎼 Capabilities: Tempo, key, energy, spectral analysis")
    print(f"   🧠 Features: 40+ audio features per track")
    print(f"   📊 Analyzer stats: {analyzer.get_stats()}")
    
    # Component 5: Pattern Recognizer (Sprint 2)
    print_section("5. Pattern Recognizer - Organization Intelligence")
    recognizer = PatternRecognizer()
    print(f"   🔍 Detects: Folder structures, naming conventions, metadata patterns")
    print(f"   📈 Confidence scoring and frequency tracking")
    print(f"   📊 Recognizer stats: {recognizer.get_stats()}")
    
    # Component 6: Comment Analyzer (Sprint 2)
    print_section("6. Comment Analyzer - Natural Language Understanding")
    comment_analyzer = CommentAnalyzer()
    print(f"   💬 NLP Engine: spaCy-powered vocabulary extraction")
    print(f"   🎯 Features: Style analysis, sentiment, phrase detection")
    print(f"   📊 Analyzer stats: {comment_analyzer.get_stats()}")
    
    # Component 7: Pattern Learner (Sprint 2)
    print_section("7. Pattern Learner - Machine Learning Brain")
    learner = PatternLearner()
    print(f"   🤖 ML Models: Random Forest, Gradient Boosting")
    print(f"   🎯 Multi-modal: Audio + Metadata + Text features")
    print(f"   📊 Learner stats: {learner.get_stats()}")


def demo_ai_integration():
    """Demonstrate the complete AI integration."""
    print_header("COMPLETE AI INTEGRATION DEMO")
    
    print("The AIOrganizer brings all components together into a unified intelligent system:")
    
    # Initialize AI Organizer
    print_section("Initializing AI Organizer")
    try:
        organizer = AIOrganizer()
        print("✅ AI Organizer initialized successfully!")
        print("   🔗 All components integrated and ready")
        
        # Show comprehensive stats
        print_section("System Statistics")
        stats = organizer.get_stats()
        
        print("📊 Component Status:")
        for component, component_stats in stats.items():
            if isinstance(component_stats, dict):
                print(f"   • {component}: {len(component_stats)} metrics tracked")
            else:
                print(f"   • {component}: {component_stats}")
        
        organizer.close()
        
    except Exception as e:
        print(f"❌ Error initializing AI Organizer: {e}")


def demo_sample_analysis():
    """Demonstrate analysis with sample data."""
    print_header("SAMPLE ANALYSIS DEMO")
    
    print("Here's what the AI can do with your music collection:")
    
    print_section("Sample Comment Analysis")
    comment_analyzer = CommentAnalyzer()
    
    sample_comments = [
        "Great energy track with pumping bass",
        "Smooth jazz piece, very relaxing",
        "Love the ambient soundscape",
        "Excellent production quality",
        "Dark and moody electronic music",
        "Uplifting pop song with great vocals"
    ]
    
    print("📝 Sample comments:")
    for i, comment in enumerate(sample_comments, 1):
        print(f"   {i}. \"{comment}\"")
    
    analysis = comment_analyzer.analyze_comments(sample_comments)
    
    print(f"\n🧠 AI Analysis Results:")
    print(f"   • Vocabulary patterns detected: {len(analysis.vocabulary_patterns)}")
    print(f"   • Writing style patterns: {len(analysis.style_patterns)}")
    print(f"   • Common phrases: {len(analysis.common_phrases)}")
    print(f"   • Sentiment distribution: {analysis.sentiment_distribution}")
    
    # Generate suggestions
    suggestions = comment_analyzer.generate_comment_suggestions(analysis, {})
    print(f"\n💡 AI-Generated Comment Suggestions:")
    for i, suggestion in enumerate(suggestions, 1):
        print(f"   {i}. \"{suggestion}\"")


def demo_capabilities():
    """Show what the AI can do."""
    print_header("AI CAPABILITIES SHOWCASE")
    
    capabilities = [
        ("🎵 Audio Analysis", "Analyzes tempo, key, energy, and 40+ spectral features"),
        ("🔍 Pattern Recognition", "Learns your organization style from existing collections"),
        ("💬 Language Understanding", "Understands your vocabulary and writing style"),
        ("🤖 Intelligent Prediction", "Suggests organization with confidence scoring"),
        ("📊 Progress Tracking", "Real-time progress for long operations"),
        ("💾 Smart Storage", "Efficient database with backup capabilities"),
        ("🎯 Multi-Modal Learning", "Combines audio, metadata, and text analysis"),
        ("📈 Performance Monitoring", "Tracks accuracy and system performance"),
    ]
    
    print("The AI Music Organization Assistant can:")
    print()
    
    for capability, description in capabilities:
        print(f"{capability}")
        print(f"   {description}")
        print()


def main():
    """Run the complete demo."""
    try:
        # Core components demo
        demo_core_components()
        
        # AI integration demo
        demo_ai_integration()
        
        # Sample analysis demo
        demo_sample_analysis()
        
        # Capabilities showcase
        demo_capabilities()
        
        # Final summary
        print_header("DEMO COMPLETE")
        print("🎉 The AI Music Organization Assistant is ready!")
        print()
        print("Next Steps:")
        print("• Run 'python main.py' for the GUI interface")
        print("• Run 'python main.py --cli' for command-line interface")
        print("• Run 'python main.py --scan /path/to/music' to analyze a collection")
        print()
        print("Sprint 3 will add:")
        print("• Automatic file organization")
        print("• User confirmation workflows")
        print("• Batch processing capabilities")
        print("• Advanced GUI features")
        
    except KeyboardInterrupt:
        print("\n\nDemo interrupted by user.")
    except Exception as e:
        print(f"\nDemo error: {e}")


if __name__ == "__main__":
    main()
