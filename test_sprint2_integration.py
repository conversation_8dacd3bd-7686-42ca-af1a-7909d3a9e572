#!/usr/bin/env python3
"""
Sprint 2 Integration Test for AI Music Organization Assistant

This script tests the complete Sprint 2 integration including audio analysis,
pattern recognition, NLP analysis, and machine learning components.

Author: AI Music Organization Assistant Project
"""

import sys
import tempfile
from pathlib import Path
import numpy as np

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src import (
    AudioAnalyzer, AudioFeatures,
    PatternRecognizer, OrganizationalPattern,
    CommentAnalyzer, CommentAnalysis,
    PatternLearner, PredictionResult
)
from src.ai_organizer import AIOrganizer


def test_audio_analyzer():
    """Test AudioAnalyzer functionality."""
    print("Testing AudioAnalyzer...")
    
    analyzer = AudioAnalyzer()
    
    # Test with non-existent file (should handle gracefully)
    features = analyzer.analyze_audio("nonexistent.mp3")

    if features is None:
        print("✓ AudioAnalyzer handled non-existent file correctly (returned None)")
    elif features and features.analysis_errors:
        print("✓ AudioAnalyzer handled non-existent file correctly (returned features with errors)")
    else:
        print("✗ AudioAnalyzer should return None or features with errors for non-existent file")
        return False
    
    # Test stats
    stats = analyzer.get_stats()
    print(f"  - Analyzer stats: {stats}")
    
    print("✓ AudioAnalyzer initialized and tested successfully")
    return True


def test_pattern_recognizer():
    """Test PatternRecognizer functionality."""
    print("\nTesting PatternRecognizer...")
    
    recognizer = PatternRecognizer()
    
    # Test with current directory (should work without errors)
    patterns = recognizer.analyze_collection(".")
    
    print(f"✓ PatternRecognizer completed analysis")
    print(f"  - Patterns detected: {len(patterns)}")
    
    # Test stats
    stats = recognizer.get_stats()
    print(f"  - Recognizer stats: {stats}")
    
    return True


def test_comment_analyzer():
    """Test CommentAnalyzer functionality."""
    print("\nTesting CommentAnalyzer...")
    
    analyzer = CommentAnalyzer()
    
    # Test with sample comments
    sample_comments = [
        "Great energy track with pumping bass",
        "Smooth jazz piece, very relaxing",
        "Love the ambient soundscape",
        "Excellent production quality",
        "Dark and moody electronic music",
        "Uplifting pop song with great vocals",
        "Heavy metal with intense drums",
        "Chill downtempo for studying"
    ]
    
    analysis = analyzer.analyze_comments(sample_comments)
    
    print("✓ CommentAnalyzer completed analysis")
    print(f"  - Total comments: {analysis.total_comments}")
    print(f"  - Vocabulary patterns: {len(analysis.vocabulary_patterns)}")
    print(f"  - Style patterns: {len(analysis.style_patterns)}")
    print(f"  - Common phrases: {len(analysis.common_phrases)}")
    print(f"  - Sentiment distribution: {analysis.sentiment_distribution}")
    
    # Test suggestion generation
    suggestions = analyzer.generate_comment_suggestions(analysis, {})
    print(f"  - Generated suggestions: {suggestions}")
    
    # Test stats
    stats = analyzer.get_stats()
    print(f"  - Analyzer stats: {stats}")
    
    return True


def test_pattern_learner():
    """Test PatternLearner functionality."""
    print("\nTesting PatternLearner...")
    
    learner = PatternLearner()
    
    # Create mock training data
    print("  Creating mock training data...")
    
    # Mock song metadata - create balanced samples for better training
    songs_metadata = []
    genres = ['Rock', 'Jazz', 'Electronic']

    # Create multiple samples per genre for balanced training
    for genre in genres:
        for i in range(4):  # 4 samples per genre = 12 total
            artist = f'{genre}Artist{i+1}'
            album = f'{genre}Album{i+1}'
            songs_metadata.append({
                'file_path': f'/music/{genre}/{artist}/{album}/song{i+1}.mp3',
                'title': f'{genre} Song {i+1}', 'artist': artist, 'album': album,
                'genre': genre, 'year': 2020 + i, 'duration': 180.0 + i * 10,
                'bitrate': 320, 'file_format': '.mp3'
            })
    
    # Mock audio features
    audio_features = []
    for i in range(len(songs_metadata)):
        features = AudioFeatures(
            file_path=songs_metadata[i]['file_path'],
            duration=songs_metadata[i]['duration'],
            sample_rate=44100,
            tempo=120.0 + i * 5,
            energy_level=['medium', 'low', 'high'][i % 3],
            rms_energy=0.5 + (i % 3) * 0.1,
            mfcc_features=[0.1 + (i % 3) * 0.05] * 13,
            chroma_features=[0.1 + (i % 3) * 0.05] * 12
        )
        audio_features.append(features)
    
    # Mock organizational patterns
    patterns = [
        OrganizationalPattern(
            pattern_id='genre_org_1',
            pattern_type='genre_organization',
            pattern_description='Organizes by genre at depth 1',
            folder_patterns=[],
            naming_patterns=[],
            metadata_patterns={},
            confidence_score=0.8,
            usage_frequency=3
        )
    ]
    
    # Mock comment analysis
    comment_analysis = CommentAnalysis(
        total_comments=3,
        vocabulary_patterns=[],
        style_patterns=[],
        common_phrases=[],
        sentiment_distribution={'positive': 0.7, 'neutral': 0.2, 'negative': 0.1},
        avg_comment_length=25.0,
        language_detected='en',
        analysis_time='2024-01-01T00:00:00'
    )
    
    try:
        # Prepare training data
        X, y = learner.prepare_training_data(
            songs_metadata, audio_features, patterns, comment_analysis
        )
        
        print(f"  - Training data shape: {X.shape}")
        print(f"  - Target classes: {len(set(y))}")
        
        # Train models
        performance = learner.train_models(X, y)
        
        print("✓ PatternLearner training completed")
        print(f"  - Accuracy: {performance.accuracy:.3f}")
        print(f"  - F1 Score: {performance.f1_score:.3f}")
        print(f"  - Training samples: {performance.training_samples}")
        
        # Test prediction
        prediction = learner.predict_organization(
            songs_metadata[0], audio_features[0], comment_analysis
        )
        
        print("✓ Prediction test completed")
        print(f"  - Predicted path: {prediction.predicted_path}")
        print(f"  - Confidence: {prediction.confidence_score:.3f}")
        print(f"  - Reasoning: {prediction.reasoning}")
        
        # Test model saving/loading
        with tempfile.NamedTemporaryFile(suffix='.pkl', delete=False) as tmp_file:
            model_path = Path(tmp_file.name)
        
        if learner.save_models(model_path):
            print("✓ Model saving successful")
            
            # Test loading
            new_learner = PatternLearner()
            if new_learner.load_models(model_path):
                print("✓ Model loading successful")
            else:
                print("✗ Model loading failed")
                return False
        else:
            print("✗ Model saving failed")
            return False
        
        # Clean up
        model_path.unlink()
        
        return True
        
    except Exception as e:
        print(f"✗ PatternLearner test failed: {e}")
        return False


def test_ai_organizer_integration():
    """Test complete AI Organizer integration."""
    print("\nTesting AI Organizer Integration...")
    
    try:
        # Use temporary database
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_file:
            temp_db_path = Path(tmp_file.name)
        
        organizer = AIOrganizer(temp_db_path)
        
        print("✓ AI Organizer initialized successfully")
        
        # Test stats
        stats = organizer.get_stats()
        print(f"  - Component stats available: {len(stats)} components")
        
        # Test with current directory (limited analysis)
        print("  - Testing collection analysis on current directory...")
        
        def progress_callback(message, current, total):
            if current % 20 == 0 or current == total:
                print(f"    {message} ({current}/{total})")
        
        # This will scan current directory but won't find audio files
        # That's expected and tests the error handling
        report = organizer.analyze_collection(
            ".", 
            progress_callback=progress_callback,
            analyze_audio=False,  # Skip audio analysis for speed
            train_models=False    # Skip training since no audio files
        )
        
        print("✓ Collection analysis completed")
        print(f"  - Total files: {report.total_files}")
        print(f"  - Analyzed files: {report.analyzed_files}")
        print(f"  - Patterns found: {len(report.organizational_patterns)}")
        print(f"  - Analysis time: {report.analysis_time:.2f}s")
        
        # Test report saving
        with tempfile.NamedTemporaryFile(suffix='.json', delete=False) as tmp_file:
            report_path = Path(tmp_file.name)
        
        if organizer.save_analysis_report(report, report_path):
            print("✓ Report saving successful")
            report_path.unlink()
        else:
            print("✗ Report saving failed")
        
        # Clean up
        organizer.close()
        temp_db_path.unlink()
        
        return True
        
    except Exception as e:
        print(f"✗ AI Organizer integration test failed: {e}")
        return False


def main():
    """Run all Sprint 2 integration tests."""
    print("=" * 70)
    print("AI Music Organization Assistant - Sprint 2 Integration Tests")
    print("=" * 70)
    
    tests = [
        test_audio_analyzer,
        test_pattern_recognizer,
        test_comment_analyzer,
        test_pattern_learner,
        test_ai_organizer_integration,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print("Test failed!")
        except Exception as e:
            print(f"Test error: {e}")
    
    print("\n" + "=" * 70)
    print(f"Sprint 2 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All Sprint 2 tests passed! Pattern recognition system is working correctly.")
        print("\nSprint 2 - Pattern Recognition Intelligence: COMPLETE")
        print("✓ Audio analysis with librosa")
        print("✓ Pattern recognition for folder structures")
        print("✓ NLP analysis of comment fields")
        print("✓ Machine learning models for prediction")
        print("✓ Complete AI organizer integration")
        return 0
    else:
        print("❌ Some Sprint 2 tests failed. Please check the implementation.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
