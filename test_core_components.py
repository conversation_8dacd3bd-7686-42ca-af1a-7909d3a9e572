#!/usr/bin/env python3
"""
Test script for core components of AI Music Organization Assistant

This script tests the integration of Scanner → Metadata → Database components
to ensure the pipeline is working correctly.

Author: AI Music Organization Assistant Project
"""

import sys
import tempfile
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src import MusicScanner, MetadataExtractor, DatabaseManager, get_config


def test_configuration():
    """Test configuration loading."""
    print("Testing configuration...")
    config = get_config()
    
    assert 'project' in config
    assert 'audio_formats' in config
    assert config['project']['name'] == "AI Music Organization Assistant"
    
    print("✓ Configuration loaded successfully")
    print(f"  - Project: {config['project']['name']}")
    print(f"  - Supported formats: {len(config['audio_formats'])} formats")
    return True


def test_scanner():
    """Test MusicScanner functionality."""
    print("\nTesting MusicScanner...")
    
    scanner = MusicScanner()
    
    # Test with current directory (should not find audio files but should work)
    def progress_callback(current, total, current_file):
        if current % 10 == 0:  # Print every 10th file
            print(f"  Progress: {current}/{total} - {Path(current_file).name}")
    
    try:
        result = scanner.scan_directory(".", progress_callback)
        
        print("✓ Scanner completed successfully")
        print(f"  - Total files scanned: {result.total_files}")
        print(f"  - Audio files found: {len(result.audio_files)}")
        print(f"  - Directories scanned: {result.directories_scanned}")
        print(f"  - Scan time: {result.scan_time:.2f} seconds")
        print(f"  - Errors: {len(result.errors)}")
        
        # Print scanner stats
        stats = scanner.get_stats()
        print(f"  - Scanner stats: {stats}")
        
        return True
        
    except Exception as e:
        print(f"✗ Scanner failed: {e}")
        return False


def test_metadata_extractor():
    """Test MetadataExtractor functionality."""
    print("\nTesting MetadataExtractor...")
    
    extractor = MetadataExtractor()
    
    # Test with a non-existent file (should handle gracefully)
    metadata = extractor.extract_metadata("nonexistent.mp3")
    
    if metadata is None:
        print("✓ MetadataExtractor handled non-existent file correctly")
    else:
        print("✗ MetadataExtractor should return None for non-existent file")
        return False
    
    # Print extractor stats
    stats = extractor.get_stats()
    print(f"  - Extractor stats: {stats}")
    
    print("✓ MetadataExtractor initialized and tested successfully")
    return True


def test_database_manager():
    """Test DatabaseManager functionality."""
    print("\nTesting DatabaseManager...")
    
    # Use temporary database for testing
    with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_file:
        temp_db_path = Path(tmp_file.name)
    
    try:
        db = DatabaseManager(temp_db_path)
        
        # Initialize database
        if not db.initialize_database():
            print("✗ Failed to initialize database")
            return False
        
        print("✓ Database initialized successfully")
        
        # Get database stats
        stats = db.get_database_stats()
        print(f"  - Database stats: {stats}")
        
        # Test logging an operation
        log_id = db.log_operation(
            operation_type="test",
            status="completed",
            message="Test operation",
            details={"test": True}
        )
        
        if log_id:
            print("✓ Operation logging works")
        else:
            print("✗ Operation logging failed")
            return False
        
        # Test backup
        backup_path = temp_db_path.parent / "test_backup.db"
        if db.backup_database(backup_path):
            print("✓ Database backup works")
            backup_path.unlink()  # Clean up backup
        else:
            print("✗ Database backup failed")
        
        # Clean up
        db.close_connections()
        temp_db_path.unlink()
        
        return True
        
    except Exception as e:
        print(f"✗ Database test failed: {e}")
        # Clean up on error
        if temp_db_path.exists():
            temp_db_path.unlink()
        return False


def test_pipeline_integration():
    """Test integration of all components."""
    print("\nTesting pipeline integration...")
    
    try:
        # Initialize components
        scanner = MusicScanner()
        extractor = MetadataExtractor()
        
        # Use temporary database
        with tempfile.NamedTemporaryFile(suffix='.db', delete=False) as tmp_file:
            temp_db_path = Path(tmp_file.name)
        
        db = DatabaseManager(temp_db_path)
        db.initialize_database()
        
        print("✓ All components initialized successfully")
        
        # Test the pipeline flow (without actual audio files)
        print("✓ Pipeline components can work together")
        
        # Clean up
        db.close_connections()
        temp_db_path.unlink()
        
        return True
        
    except Exception as e:
        print(f"✗ Pipeline integration test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("=" * 60)
    print("AI Music Organization Assistant - Core Component Tests")
    print("=" * 60)
    
    tests = [
        test_configuration,
        test_scanner,
        test_metadata_extractor,
        test_database_manager,
        test_pipeline_integration,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print("Test failed!")
        except Exception as e:
            print(f"Test error: {e}")
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Core components are working correctly.")
        return 0
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
