#!/usr/bin/env python3
"""
Automatic Sprint Progression System

This script integrates the context updater and sprint progression tracker
to provide seamless automatic advancement through project sprints.

Usage:
    python auto_progression.py --check-and-advance
    python auto_progression.py --update-task "Completed scanner implementation"
"""

import argparse
from pathlib import Path
from update_context import <PERSON><PERSON><PERSON>pdater
from sprint_progression import SprintProgressionTracker
from typing import Optional

class AutoProgressionManager:
    """Manages automatic sprint progression and context updates"""
    
    def __init__(self):
        self.context_updater = ContextUpdater()
        self.sprint_tracker = SprintProgressionTracker()
    
    def check_and_advance(self) -> bool:
        """Check current sprint status and advance if ready"""
        # Get current sprint status
        status = self.sprint_tracker.check_current_sprint_status()
        
        print(f"Current Sprint: {status['sprint']}")
        print(f"Completion: {status['completion_percentage']:.1f}%")
        
        # Check if sprint should advance
        if self.sprint_tracker.should_advance_sprint():
            print("🎉 All gates completed! Advancing sprint...")
            
            if self.sprint_tracker.advance_sprint():
                # Update context with new sprint info
                new_sprint = self.sprint_tracker.state_data.get('current_sprint')
                new_phase = self.sprint_tracker.state_data.get('current_phase')
                new_task = self.sprint_tracker.state_data.get('current_task')
                
                self.context_updater.state_data.update({
                    'current_sprint': new_sprint,
                    'current_phase': new_phase,
                    'current_task': new_task,
                    'sprint_progress': 0.0
                })
                
                self.context_updater.sync_all()
                print(f"✅ Advanced to: {new_sprint}")
                return True
            else:
                print("❌ Failed to advance sprint")
                return False
        else:
            print("⏳ Sprint not ready for advancement")
            incomplete_gates = [g for g in status['gates'] if not g['completed']]
            print(f"Remaining gates: {len(incomplete_gates)}")
            for gate in incomplete_gates[:3]:  # Show first 3
                print(f"  - {gate['description']}")
            return False
    
    def update_task_and_check(self, completed_task: str, current_task: Optional[str] = None, 
                             next_task: Optional[str] = None, evidence: Optional[list] = None) -> None:
        """Update task progress and check for sprint advancement"""
        # Update context
        self.context_updater.update_task_progress(completed_task, current_task, next_task)
        
        # Check if this completion affects any gates
        if evidence:
            # Try to find matching gates and update them
            for gate_desc in self.sprint_tracker.gate_criteria.keys():
                if any(keyword in completed_task.lower() for keyword in gate_desc.lower().split()):
                    is_complete, confidence, missing = self.sprint_tracker.evaluate_gate_completion(
                        gate_desc, evidence
                    )
                    if is_complete:
                        self.sprint_tracker.update_gate_status(gate_desc, True, evidence, confidence)
                        print(f"✅ Gate completed: {gate_desc}")
        
        # Check for advancement
        self.check_and_advance()

def main():
    parser = argparse.ArgumentParser(description="Automatic Sprint Progression Manager")
    parser.add_argument('--check-and-advance', action='store_true', 
                       help='Check current status and advance if ready')
    parser.add_argument('--update-task', metavar='TASK', 
                       help='Mark task as completed and check advancement')
    parser.add_argument('--current-task', metavar='TASK', 
                       help='Set current task')
    parser.add_argument('--next-task', metavar='TASK', 
                       help='Set next task')
    parser.add_argument('--evidence', nargs='+', 
                       help='Evidence for task completion')
    
    args = parser.parse_args()
    manager = AutoProgressionManager()
    
    if args.check_and_advance:
        manager.check_and_advance()
    
    if args.update_task:
        manager.update_task_and_check(
            args.update_task, 
            args.current_task, 
            args.next_task, 
            args.evidence
        )

if __name__ == "__main__":
    main()
