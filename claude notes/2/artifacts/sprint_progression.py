#!/usr/bin/env python3
"""
Sprint Progression Tracker for AI Music Organization Assistant

This module provides automated sprint gate checking and progression logic.
It monitors completed tasks and automatically advances sprints when all gates are met.

Usage:
    from sprint_progression import SprintProgressionTracker
    
    tracker = SprintProgressionTracker()
    tracker.check_current_sprint_status()
    tracker.evaluate_gate_completion("Can scan directories recursively")

Author: AI Music Organization Assistant Project
"""

import json
import re
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, field


@dataclass
class Gate:
    """Represents a sprint gate with completion criteria"""
    description: str
    completed: bool = False
    completion_date: Optional[str] = None
    confidence: float = 0.0
    evidence: List[str] = field(default_factory=list)
    
    def __post_init__(self):
        if self.evidence is None:
            self.evidence = []


class SprintProgressionTracker:
    """Manages sprint progression and gate completion logic"""
    
    def __init__(self, state_file: str = "project_state.json"):
        self.state_file = Path(state_file)
        self.state_data = self._load_state()
        
        # Define gate completion criteria
        self.gate_criteria = self._define_gate_criteria()
    
    def _load_state(self) -> Dict:
        """Load current project state from JSON file"""
        try:
            with open(self.state_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"Error: {self.state_file} not found!")
            return {}
        except json.JSONDecodeError as e:
            print(f"Error parsing JSON: {e}")
            return {}
    
    def _save_state(self) -> None:
        """Save current project state to JSON file"""
        self.state_data['last_updated'] = datetime.utcnow().isoformat() + 'Z'
        
        with open(self.state_file, 'w', encoding='utf-8') as f:
            json.dump(self.state_data, f, indent=4, ensure_ascii=False)
    
    def _define_gate_criteria(self) -> Dict:
        """Define the criteria for completing each sprint gate"""
        return {
            # Sprint 1 Gates
            "Can scan directories recursively with progress tracking": {
                "required_files": ["src/scanner.py"],
                "required_classes": ["MusicScanner"],
                "required_methods": ["scan_directory", "track_progress"],
                "required_functionality": ["recursive_scan", "progress_callback"],
                "test_criteria": ["handles_nested_directories", "reports_progress"]
            },
            
            "Successfully extracts metadata from all supported formats": {
                "required_files": ["src/metadata.py"],
                "required_classes": ["MetadataExtractor"],
                "required_methods": ["extract_metadata", "normalize_metadata"],
                "supported_formats": [".mp3", ".flac", ".wav", ".m4a", ".ogg", ".aac", ".wma"],
                "test_criteria": ["handles_all_formats", "error_handling"]
            },
            
            "Database stores and retrieves song information correctly": {
                "required_files": ["src/database.py"],
                "required_classes": ["DatabaseManager"],
                "required_methods": ["create_tables", "insert_song", "query_songs"],
                "database_schema": ["songs", "patterns", "processing_log"],
                "test_criteria": ["data_persistence", "query_performance"]
            },
            
            "Basic GUI allows directory selection and shows progress": {
                "required_files": ["src/gui.py"],
                "required_classes": ["MainWindow"],
                "required_methods": ["select_directory", "show_progress"],
                "gui_components": ["directory_browser", "progress_bar", "status_display"],
                "test_criteria": ["user_interaction", "visual_feedback"]
            },
            
            "Error handling works for common failure scenarios": {
                "required_functionality": ["graceful_failure", "informative_logging"],
                "error_scenarios": ["file_permission_errors", "corrupted_files", "network_interruption"],
                "test_criteria": ["no_crashes", "helpful_error_messages"]
            },
            
            # Sprint 2 Gates
            "Audio feature extraction working reliably": {
                "required_files": ["src/audio_analysis.py"],
                "required_classes": ["AudioAnalyzer"],
                "required_methods": ["extract_features", "analyze_tempo", "detect_key"],
                "audio_features": ["tempo", "key", "energy", "spectral_features"],
                "test_criteria": ["consistent_results", "performance_acceptable"]
            },
            
            "Pattern recognition identifies folder structures": {
                "required_files": ["src/pattern_recognition.py"],
                "required_classes": ["PatternRecognizer"],
                "required_methods": ["analyze_folder_structure", "detect_patterns"],
                "pattern_types": ["hierarchy_patterns", "naming_conventions", "grouping_logic"],
                "test_criteria": ["pattern_detection", "confidence_scoring"]
            },
            
            "Metadata patterns detected and scored": {
                "required_methods": ["analyze_metadata_patterns", "score_consistency"],
                "metadata_patterns": ["field_usage", "formatting_style", "vocabulary"],
                "test_criteria": ["pattern_accuracy", "scoring_reliability"]
            },
            
            "Comment analysis extracts user vocabulary": {
                "required_files": ["src/nlp_analysis.py"],
                "required_classes": ["CommentAnalyzer"],
                "required_methods": ["analyze_comments", "extract_vocabulary"],
                "nlp_features": ["keyword_extraction", "phrase_identification", "style_analysis"],
                "test_criteria": ["vocabulary_extraction", "style_recognition"]
            },
            
            "Machine learning models train on real data": {
                "required_files": ["src/ml_models.py"],
                "required_classes": ["PatternLearner"],
                "required_methods": ["train_model", "predict_organization"],
                "ml_components": ["feature_vectors", "training_pipeline", "model_persistence"],
                "test_criteria": ["model_convergence", "prediction_accuracy"]
            },
            
            # Sprint 3 Gates
            "Automatic file organization with confidence scoring": {
                "required_files": ["src/organization_engine.py"],
                "required_classes": ["OrganizationEngine"],
                "required_methods": ["organize_file", "calculate_confidence"],
                "organization_features": ["automatic_placement", "confidence_scoring", "batch_processing"],
                "test_criteria": ["organization_accuracy", "confidence_calibration"]
            },
            
            "Metadata enhancement follows detected patterns": {
                "required_methods": ["enhance_metadata", "apply_patterns"],
                "enhancement_features": ["field_formatting", "vocabulary_application", "consistency_enforcement"],
                "test_criteria": ["pattern_adherence", "quality_improvement"]
            },
            
            "Batch processing handles multiple files safely": {
                "required_methods": ["process_batch", "handle_errors"],
                "batch_features": ["queue_management", "progress_tracking", "error_recovery"],
                "test_criteria": ["batch_reliability", "error_handling"]
            },
            
            "Backup and rollback mechanisms work correctly": {
                "required_files": ["src/backup_manager.py"],
                "required_classes": ["BackupManager"],
                "required_methods": ["create_backup", "rollback_changes"],
                "backup_features": ["automatic_backup", "rollback_capability", "integrity_checking"],
                "test_criteria": ["backup_reliability", "rollback_success"]
            },
            
            "Complete workflow from scan to organization": {
                "required_functionality": ["end_to_end_workflow"],
                "workflow_stages": ["scan", "analyze", "learn", "organize"],
                "test_criteria": ["workflow_completion", "integration_success"]
            },
            
            # Sprint 4 Gates
            "GUI provides full functionality with good UX": {
                "required_files": ["src/advanced_gui.py"],
                "gui_features": ["pattern_visualization", "manual_override", "progress_display"],
                "ux_requirements": ["intuitive_interface", "responsive_design", "helpful_feedback"],
                "test_criteria": ["user_satisfaction", "interface_completeness"]
            },
            
            "System learns from user corrections": {
                "required_classes": ["LearningSystem"],
                "required_methods": ["process_feedback", "update_model"],
                "learning_features": ["feedback_integration", "model_updating", "improvement_tracking"],
                "test_criteria": ["learning_effectiveness", "adaptation_speed"]
            },
            
            "Performance acceptable for large collections": {
                "performance_requirements": ["10k_files_handling", "reasonable_response_time"],
                "optimization_features": ["caching", "multithreading", "progressive_loading"],
                "test_criteria": ["performance_benchmarks", "scalability"]
            },
            
            "All error conditions handled gracefully": {
                "error_handling": ["comprehensive_coverage", "graceful_degradation"],
                "error_scenarios": ["all_failure_modes", "edge_cases"],
                "test_criteria": ["error_coverage", "graceful_handling"]
            },
            
            "Documentation complete for key features": {
                "documentation_requirements": ["user_guide", "api_documentation", "setup_instructions"],
                "documentation_quality": ["clear_explanations", "examples", "troubleshooting"],
                "test_criteria": ["documentation_completeness", "user_comprehension"]
            }
        }
    
    def evaluate_gate_completion(self, gate_description: str, 
                                evidence: Optional[List[str]] = None) -> Tuple[bool, float, List[str]]:
        """Evaluate whether a specific gate should be considered complete"""
        if evidence is None:
            evidence = []
        
        criteria = self.gate_criteria.get(gate_description, {})
        if not criteria:
            return False, 0.0, ["Gate criteria not found"]
        
        # Initialize evaluation metrics
        confidence = 0.0
        missing_items = []
        
        # Check for required files
        required_files = criteria.get('required_files', [])
        for file_path in required_files:
            if not Path(file_path).exists():
                missing_items.append(f"Missing file: {file_path}")
        
        # Check for required classes and methods
        # This is a simplified check - in a real system, we would parse the code
        required_classes = criteria.get('required_classes', [])
        required_methods = criteria.get('required_methods', [])
        
        # Calculate confidence based on evidence and criteria
        total_criteria = len(required_files) + len(required_classes) + len(required_methods)
        if total_criteria > 0:
            # Simple heuristic: each piece of evidence increases confidence
            confidence = min(1.0, len(evidence) / total_criteria)
        
        # Determine if gate is complete based on confidence threshold
        is_complete = confidence >= 0.8 and not missing_items
        
        return is_complete, confidence, missing_items
    
    def check_current_sprint_status(self) -> Dict:
        """Check the status of all gates in the current sprint"""
        current_sprint = self._extract_sprint_name(self.state_data.get('current_sprint', ''))
        sprint_gates = self._get_sprint_gates(current_sprint)
        
        results = {
            'sprint': current_sprint,
            'total_gates': len(sprint_gates),
            'completed_gates': 0,
            'gates': []
        }
        
        for gate_desc in sprint_gates:
            is_complete, confidence, missing = self.evaluate_gate_completion(gate_desc)
            
            gate_info = {
                'description': gate_desc,
                'completed': is_complete,
                'confidence': confidence,
                'missing_items': missing
            }
            
            results['gates'].append(gate_info)
            if is_complete:
                results['completed_gates'] += 1
        
        # Calculate overall sprint completion percentage
        if results['total_gates'] > 0:
            results['completion_percentage'] = (results['completed_gates'] / results['total_gates']) * 100
        else:
            results['completion_percentage'] = 0
            
        return results
    
    def _extract_sprint_name(self, sprint_title: str) -> str:
        """Extract sprint name from title (e.g., 'Sprint 1 - File System Foundation' -> 'Sprint 1')"""
        match = re.match(r'(Sprint \d+)', sprint_title)
        if match:
            return match.group(1)
        return sprint_title
    
    def _get_sprint_gates(self, sprint_name: str) -> List[str]:
        """Get list of gate descriptions for a given sprint"""
        gates = []
        for gate_desc in self.gate_criteria.keys():
            # Check if this gate belongs to the current sprint
            # This is a simple heuristic based on the gate criteria structure
            if sprint_name == "Sprint 1" and any(x in gate_desc for x in ["scan", "metadata", "database", "GUI", "Error handling"]):
                gates.append(gate_desc)
            elif sprint_name == "Sprint 2" and any(x in gate_desc for x in ["Audio feature", "Pattern recognition", "Metadata patterns", "Comment analysis", "Machine learning"]):
                gates.append(gate_desc)
            elif sprint_name == "Sprint 3" and any(x in gate_desc for x in ["Automatic file", "Metadata enhancement", "Batch processing", "Backup", "Complete workflow"]):
                gates.append(gate_desc)
            elif sprint_name == "Sprint 4" and any(x in gate_desc for x in ["GUI provides", "System learns", "Performance", "All error", "Documentation"]):
                gates.append(gate_desc)
        
        return gates
    
    def update_gate_status(self, gate_description: str, completed: bool, 
                          evidence: Optional[List[str]] = None, confidence: Optional[float] = None) -> bool:
        """Update the status of a specific gate in the project state"""
        if evidence is None:
            evidence = []
            
        # Find the sprint this gate belongs to
        sprint_name = None
        for sprint in ["Sprint 1", "Sprint 2", "Sprint 3", "Sprint 4"]:
            if gate_description in self._get_sprint_gates(sprint):
                sprint_name = sprint
                break
        
        if not sprint_name:
            return False
            
        # Update the gate in the state data
        sprint_gates = self.state_data.get('sprint_gates', {}).get(sprint_name, {}).get('gates', [])
        
        for gate in sprint_gates:
            if gate['description'] == gate_description:
                gate['completed'] = completed
                if completed and not gate.get('completion_date'):
                    gate['completion_date'] = datetime.utcnow().isoformat() + 'Z'
                
                # Update evidence and confidence if provided
                if evidence:
                    gate['evidence'] = evidence
                if confidence is not None:
                    gate['confidence'] = confidence
                
                # Update completed gates count
                self.state_data['sprint_gates'][sprint_name]['completed_gates'] = sum(
                    1 for g in sprint_gates if g.get('completed', False)
                )
                
                # Save the updated state
                self._save_state()
                return True
        
        return False
    
    def should_advance_sprint(self) -> bool:
        """Check if all gates in the current sprint are completed"""
        current_sprint = self._extract_sprint_name(self.state_data.get('current_sprint', ''))
        sprint_data = self.state_data.get('sprint_gates', {}).get(current_sprint, {})
        
        total_gates = sprint_data.get('total_gates', 0)
        completed_gates = sprint_data.get('completed_gates', 0)
        
        return total_gates > 0 and completed_gates >= total_gates
    
    def advance_sprint(self) -> bool:
        """Advance to the next sprint if current sprint is complete"""
        if not self.should_advance_sprint():
            return False
            
        current_sprint = self._extract_sprint_name(self.state_data.get('current_sprint', ''))
        
        # Define sprint progression
        sprint_progression = {
            "Sprint 1": {
                "next": "Sprint 2 - Pattern Recognition Intelligence",
                "phase": "Audio Analysis Engine Setup",
                "task": "Implement audio feature extraction using librosa"
            },
            "Sprint 2": {
                "next": "Sprint 3 - Organization Engine",
                "phase": "Intelligent File Organization",
                "task": "Build core organization system"
            },
            "Sprint 3": {
                "next": "Sprint 4 - User Experience & Refinement",
                "phase": "Advanced GUI Implementation",
                "task": "Create comprehensive user interface"
            },
            "Sprint 4": {
                "next": "Project Complete",
                "phase": "Final Testing and Documentation",
                "task": "Perform end-to-end testing"
            }
        }
        
        # Get next sprint info
        next_sprint_info = sprint_progression.get(current_sprint)
        if not next_sprint_info:
            return False
            
        # Update state with next sprint info
        self.state_data['current_sprint'] = next_sprint_info['next']
        self.state_data['current_phase'] = next_sprint_info['phase']
        self.state_data['current_task'] = next_sprint_info['task']
        self.state_data['sprint_progress'] = 0.0  # Reset sprint progress
        
        # Save the updated state
        self._save_state()
        return True


def main():
    """Command-line interface for sprint progression tracking"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Sprint Progression Tracker")
    parser.add_argument('--status', action='store_true', help='Check current sprint status')
    parser.add_argument('--evaluate', metavar='GATE', help='Evaluate completion of a specific gate')
    parser.add_argument('--complete', metavar='GATE', help='Mark a gate as completed')
    parser.add_argument('--evidence', metavar='EVIDENCE', nargs='+', help='Evidence for gate completion')
    parser.add_argument('--advance', action='store_true', help='Advance to next sprint if current is complete')
    
    args = parser.parse_args()
    tracker = SprintProgressionTracker()
    
    if args.status:
        status = tracker.check_current_sprint_status()
        print(f"\nSprint: {status['sprint']}")
        print(f"Completion: {status['completion_percentage']:.1f}% ({status['completed_gates']}/{status['total_gates']} gates)")
        print("\nGate Status:")
        for gate in status['gates']:
            status_mark = "✓" if gate['completed'] else "✗"
            print(f"{status_mark} {gate['description']} (confidence: {gate['confidence']:.2f})")
            if not gate['completed'] and gate['missing_items']:
                for item in gate['missing_items']:
                    print(f"  - {item}")
    
    if args.evaluate:
        is_complete, confidence, missing = tracker.evaluate_gate_completion(args.evaluate, args.evidence)
        print(f"\nGate: {args.evaluate}")
        print(f"Status: {'Complete' if is_complete else 'Incomplete'}")
        print(f"Confidence: {confidence:.2f}")
        if missing:
            print("Missing items:")
            for item in missing:
                print(f"  - {item}")
    
    if args.complete:
        if tracker.update_gate_status(args.complete, True, args.evidence):
            print(f"✓ Gate marked as completed: {args.complete}")
        else:
            print(f"✗ Failed to update gate: {args.complete}")
    
    if args.advance:
        if tracker.should_advance_sprint():
            if tracker.advance_sprint():
                current_sprint = tracker.state_data.get('current_sprint', '')
                print(f"✓ Advanced to {current_sprint}")
            else:
                print("✗ Failed to advance sprint")
        else:
            print("✗ Current sprint is not complete yet")


if __name__ == "__main__":
    main()
