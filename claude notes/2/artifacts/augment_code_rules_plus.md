## MANDATORY CONTEXT UPDATES (CRITICAL - NEVER SKIP)

### After Every Task Completion:
You MUST run these commands after completing ANY implementation:

1. **Update task progress:**
   ```bash
   python auto_progression.py --update-task "Completed [specific task]" --current-task "[what you're doing now]" --next-task "[logical next step]" --evidence "[file1.py]" "[file2.py]"
   ```

2. **Check sprint advancement:**
   ```bash
   python auto_progression.py --check-and-advance
   ```

3. **Verify updates:**
   ```bash
   python update_context.py --status
   python sprint_progression.py --status
   ```

### Context Update Checklist (MANDATORY):
- [ ] `auto_progression.py --update-task` executed
- [ ] `project_state.json` updated automatically
- [ ] `augment_code_rules.md` context section synced
- [ ] Sprint gates evaluated for completion
- [ ] Sprint advancement checked
- [ ] Report changes in your response

### Response Template (USE THIS):
```
## TASK COMPLETION REPORT

**Implemented:** [Specific functionality]
**Files Modified:** [Exact paths]
**Evidence:** [Files that prove completion]

**Context Updates:**
- ✅ Task marked complete: [task name]
- ✅ Current task updated: [new current task]
- ✅ Sprint progress: [X%]
- ✅ Gates evaluated: [any completed gates]
- ✅ Sprint status: [ready/not ready for advancement]

**Auto-progression executed:** ✅
```