#!/usr/bin/env python3
"""Test the automatic progression system"""

from auto_progression import AutoProgressionManager
import json

def test_system():
    """Test the progression system with sample data"""
    manager = AutoProgressionManager()
    
    print("=== Testing Automatic Progression System ===\n")
    
    # Test 1: Check current status
    print("1. Current Status:")
    manager.check_and_advance()
    
    # Test 2: Update a task
    print("\n2. Updating task:")
    manager.update_task_and_check(
        completed_task="Created basic project structure",
        current_task="Implementing MusicScanner class",
        next_task="Add progress tracking to scanner",
        evidence=["src/scanner.py", "src/__init__.py"]
    )
    
    # Test 3: Check sprint tracker
    print("\n3. Sprint Tracker Status:")
    status = manager.sprint_tracker.check_current_sprint_status()
    print(f"Sprint: {status['sprint']}")
    print(f"Gates: {status['completed_gates']}/{status['total_gates']}")
    
    print("\n=== Test Complete ===")

if __name__ == "__main__":
    test_system()