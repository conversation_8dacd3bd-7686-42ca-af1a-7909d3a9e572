# Augment Code Project Rules - AI Music Organization Assistant

## CORE PROJECT IDENTITY
You are building an AI that learns a DJ's personal music organization style and automatically applies it to new files. This is NOT a generic music organizer - it's a personalized AI assistant that understands individual organizational DNA.

**Project Mission:** Create an AI that studies existing music collections, learns personal organizational patterns, and automatically organizes new files exactly like the user would manually.

**Target User:** Solo DJ/music enthusiast who wants to automate tedious file organization while preserving personal organizational style.

## TECHNICAL STACK (LOCKED - NO ALTERNATIVES)
- **Python 3.11** - Primary language
- **mutagen 1.47.0** - Audio metadata manipulation
- **librosa 0.10.1** - Audio analysis and feature extraction
- **scikit-learn 1.3.0** - Machine learning for pattern recognition
- **tkinter** - GUI framework (built-in, no installation required)
- **sqlite3** - Database (built-in, no setup required)
- **pandas 2.1.0** - Data manipulation
- **numpy 1.24.0** - Mathematical operations
- **spaCy 3.7.0** - Natural language processing for comments

## DEVELOPMENT PROGRESSION (STRICT ORDER)
**Current Sprint:** [UPDATE THIS MANUALLY AS YOU PROGRESS]

**Sprint 1 (Days 1-8):** File System Foundation
- Music collection scanner with progress tracking
- Metadata extraction using mutagen
- SQLite database setup with proper schema
- Basic tkinter GUI for directory selection

**Sprint 2 (Days 9-16):** Pattern Recognition Intelligence
- Audio analysis using librosa
- Folder structure pattern detection
- Metadata pattern analysis
- Comment field NLP processing

**Sprint 3 (Days 17-24):** Organization Engine
- Intelligent file organization system
- Metadata enhancement and formatting
- Batch processing framework
- Safety and backup systems

**Sprint 4 (Days 25-32):** Polish and Enhancement
- Advanced GUI with pattern visualization
- Learning and adaptation systems
- Performance optimization
- Final integration testing

## CODING PRINCIPLES (NEVER VIOLATE)

### Architecture Rules
1. **Pipeline Pattern:** Every component feeds into the next: Scanner → Metadata → Patterns → Organization
2. **Single Responsibility:** Each class has one clear purpose
3. **Error Handling:** Graceful failure with informative logging
4. **Incremental Building:** Each feature builds on previous foundation
5. **Real-world Testing:** Test with actual music files, not synthetic data

### Code Style Requirements
1. **Use descriptive class/method names:** `MusicScanner`, `MetadataExtractor`, `PatternRecognizer`
2. **Add docstrings to all classes and methods**
3. **Include type hints for all function parameters and returns**
4. **Use pathlib for file operations, not os.path**
5. **Implement proper logging with different levels (DEBUG, INFO, WARNING, ERROR)**

### Data Flow Rules
1. **Database First:** All operations must persist to SQLite
2. **Caching Strategy:** Cache expensive operations (audio analysis, pattern recognition)
3. **Progress Tracking:** Show progress for all long-running operations
4. **Atomic Operations:** File moves must be atomic (complete or rollback)
5. **Backup Before Changes:** Always backup before modifying files

## CURRENT SPRINT CONTEXT (UPDATE THIS SECTION)

**Current Sprint:** [Sprint 1 - File System Foundation]
**Current Phase:** [Music Collection Scanner Implementation]
**Last Completed:** [Environment setup and project structure]
**Current Task:** [Building MusicScanner class with os.walk()]
**Next Task:** [Add progress tracking to scanner]

**Active Files:**
- `src/scanner.py` - MusicScanner class implementation
- `src/database.py` - SQLite schema and operations
- `src/metadata.py` - MetadataExtractor class
- `src/gui.py` - Basic tkinter interface

**Key Decisions Made:**
- Using SQLite with tables: songs, patterns, processing_log
- Supported formats: .mp3, .flac, .wav, .m4a, .ogg, .aac, .wma
- Progress tracking via callback functions
- Caching scanned directories in JSON format

## IMPLEMENTATION CONSTRAINTS

### File System Operations
- **ALWAYS use pathlib.Path** for file operations
- **Handle unicode filenames** properly
- **Check file permissions** before operations
- **Log all file operations** for audit trail
- **Implement proper error recovery** for interrupted operations

### Database Operations
- **Use parameterized queries** to prevent SQL injection
- **Implement connection pooling** for performance
- **Add database migrations** for schema changes
- **Create indexes** on frequently queried columns
- **Backup database** before major operations

### Machine Learning Guidelines
- **Feature scaling** is mandatory before training
- **Cross-validation** for all model evaluation
- **Confidence scoring** for all predictions
- **Model persistence** using joblib
- **Incremental learning** capabilities for new data

### GUI Development Rules
- **Responsive interface** - no blocking operations on main thread
- **Progress indicators** for all long-running tasks
- **Error dialogs** with actionable information
- **Keyboard shortcuts** for common operations
- **Proper window management** (sizing, centering, etc.)

## PATTERN RECOGNITION SPECIFICS

### Folder Structure Analysis
- **Detect hierarchy patterns:** Genre → Subgenre → Energy level
- **Identify naming conventions:** CamelCase vs snake_case vs spaces
- **Track folder depth preferences:** How deep does user organize?
- **Recognize grouping logic:** By year, by BPM, by mood, etc.

### Metadata Pattern Detection
- **Field usage consistency:** Which fields are always filled?
- **Formatting patterns:** How are artists formatted? Genre vocabularies?
- **Comment analysis:** Extract descriptive vocabulary and style
- **Missing data patterns:** What gets left blank consistently?

### Audio Feature Integration
- **Tempo analysis:** BPM detection for DJ use
- **Key detection:** Harmonic compatibility
- **Energy levels:** Low/medium/high energy classification
- **Spectral features:** Brightness, warmth, etc.
- **Structural analysis:** Intro/outro lengths, breakdown sections

## DEBUGGING AND TESTING STRATEGY

### Testing Approach
1. **Unit tests** for individual components
2. **Integration tests** for complete workflows
3. **Real-world testing** with actual music collections
4. **Edge case testing** with corrupted/unusual files
5. **Performance testing** with large collections (10k+ files)

### Logging Strategy
- **DEBUG:** Detailed operation steps
- **INFO:** Major operations and progress
- **WARNING:** Recoverable errors or unusual conditions
- **ERROR:** Operation failures requiring attention

### Error Handling Patterns
```python
try:
    # Main operation
    result = risky_operation()
    logger.info(f"Success: {result}")
    return result
except SpecificException as e:
    logger.error(f"Operation failed: {e}")
    # Attempt recovery or graceful degradation
    return default_value
```

## FEATURE IMPLEMENTATION PRIORITIES

### Must-Have Features (Core Functionality)
1. **Reliable file scanning** with progress tracking
2. **Accurate metadata extraction** across all formats
3. **Pattern recognition** for folder structures
4. **Automatic file organization** with confidence scoring
5. **Safety mechanisms** (backup, rollback, validation)

### Should-Have Features (Enhanced Experience)
1. **Audio analysis** for similarity detection
2. **Comment generation** matching user style
3. **Batch processing** with queue management
4. **Pattern visualization** in GUI
5. **Learning from corrections** for continuous improvement

### Could-Have Features (Nice-to-Have)
1. **Advanced similarity metrics** using audio fingerprinting
2. **Multi-threading** for faster processing
3. **Cloud backup** integration
4. **Plugin system** for custom patterns
5. **Export/import** of learned patterns

## QUALITY GATES (MUST PASS BEFORE NEXT SPRINT)

### Sprint 1 Gate: Foundation Complete
- [ ] Can scan directories recursively with progress tracking
- [ ] Successfully extracts metadata from all supported formats
- [ ] Database stores and retrieves song information correctly
- [ ] Basic GUI allows directory selection and shows progress
- [ ] Error handling works for common failure scenarios

### Sprint 2 Gate: Intelligence Implemented
- [ ] Audio feature extraction working reliably
- [ ] Pattern recognition identifies folder structures
- [ ] Metadata patterns detected and scored
- [ ] Comment analysis extracts user vocabulary
- [ ] Machine learning models train on real data

### Sprint 3 Gate: Organization Functional
- [ ] Automatic file organization with confidence scoring
- [ ] Metadata enhancement follows detected patterns
- [ ] Batch processing handles multiple files safely
- [ ] Backup and rollback mechanisms work correctly
- [ ] Complete workflow from scan to organization

### Sprint 4 Gate: Production Ready
- [ ] GUI provides full functionality with good UX
- [ ] System learns from user corrections
- [ ] Performance acceptable for large collections
- [ ] All error conditions handled gracefully
- [ ] Documentation complete for key features

## COMMUNICATION PROTOCOLS

### Progress Reporting
When completing any task, report:
1. **What was implemented** (specific functionality)
2. **What files were modified** (exact paths)
3. **What was tested** (test scenarios and results)
4. **What's next** (logical next task)
5. **Any blockers** (dependencies or issues)

### Code Documentation
Every code file must include:
1. **Module docstring** explaining purpose
2. **Class docstrings** with usage examples
3. **Method docstrings** with parameters and return values
4. **Inline comments** for complex logic
5. **TODO comments** for future improvements

### Decision Tracking
Document all significant decisions:
1. **Algorithm choices** and reasoning
2. **Data structure decisions** and trade-offs
3. **Performance optimizations** and measurements
4. **User experience decisions** and rationale
5. **Technical debt** and future refactoring needs

## CONTEXT PRESERVATION RULES

### Session Continuity
1. **Always check current sprint status** before starting work
2. **Review last completed tasks** to understand current state
3. **Confirm next logical task** based on progression
4. **Update context section** after completing work
5. **Note any discoveries** or changes in approach

### File Organization
1. **Maintain consistent file structure** throughout project
2. **Use clear naming conventions** for all files
3. **Document file purposes** in README or comments
4. **Track dependencies** between files
5. **Version control** all changes with meaningful commits

### Knowledge Transfer
1. **Document design decisions** in code comments
2. **Explain complex algorithms** with examples
3. **Note performance characteristics** of implementations
4. **Record testing approaches** and results
5. **Maintain architectural overview** in documentation

## FAILURE RECOVERY PROCEDURES

### When Things Go Wrong
1. **Stop and assess** - don't continue broken implementation
2. **Check logs** for error details and stack traces
3. **Verify environment** - dependencies, versions, paths
4. **Test with minimal case** - isolate the problem
5. **Rollback if necessary** - return to last working state

### Common Issues and Solutions
- **Import errors:** Check virtual environment and package versions
- **File access errors:** Verify permissions and path existence
- **Database errors:** Check schema and connection handling
- **GUI freezing:** Ensure threading for long operations
- **Memory issues:** Implement proper resource cleanup

Remember: You're building a personalized AI assistant, not a generic tool. Every decision should serve the goal of learning and replicating individual organizational preferences. The AI should feel like an extension of the user's own organizational instincts.