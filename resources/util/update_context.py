#!/usr/bin/env python3
"""
Context Update Utility for AI Music Organization Assistant

This script synchronizes the project state between JSON and Markdown formats,
ensuring that the CURRENT SPRINT CONTEXT section in augment_code_rules.md
stays current with the project's actual progress.

Usage:
    python update_context.py [--task "completed task"] [--next "next task"]
    
Author: AI Music Organization Assistant Project
"""

import json
import re
import argparse
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional


class ContextUpdater:
    """Manages project state updates and markdown synchronization"""
    
    def __init__(self, state_file: str = "project_state.json", 
                 rules_file: str = "augment_code_rules.md"):
        self.state_file = Path(state_file)
        self.rules_file = Path(rules_file)
        self.state_data = self._load_state()
    
    def _load_state(self) -> Dict:
        """Load current project state from JSON file"""
        try:
            with open(self.state_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            return self._create_initial_state()
        except json.JSONDecodeError as e:
            print(f"Error parsing JSON: {e}")
            return self._create_initial_state()
    
    def _create_initial_state(self) -> Dict:
        """Create initial project state structure"""
        return {
            "project_info": {
                "name": "AI Music Organization Assistant",
                "version": "0.1.0",
                "start_date": datetime.utcnow().isoformat() + 'Z'
            },
            "current_sprint": "Sprint 1 - File System Foundation",
            "current_phase": "Music Collection Scanner Implementation",
            "last_completed": "Environment setup and project structure",
            "current_task": "Building MusicScanner class with os.walk()",
            "next_task": "Add progress tracking to scanner",
            "sprint_progress": 0.15,
            "overall_progress": 0.05,
            "active_files": [],
            "completed_tasks": [],
            "blockers": [],
            "notes": [],
            "sprint_gates": {}
        }
    
    def _save_state(self) -> None:
        """Save current project state to JSON file"""
        self.state_data['last_updated'] = datetime.utcnow().isoformat() + 'Z'
        with open(self.state_file, 'w', encoding='utf-8') as f:
            json.dump(self.state_data, f, indent=4, ensure_ascii=False)
    
    def update_task_progress(self, completed_task: Optional[str] = None,
                           current_task: Optional[str] = None,
                           next_task: Optional[str] = None) -> None:
        """Update task progression"""
        if completed_task:
            self.state_data['last_completed'] = completed_task
            if completed_task not in self.state_data['completed_tasks']:
                self.state_data['completed_tasks'].append(completed_task)
        
        if current_task:
            self.state_data['current_task'] = current_task
        
        if next_task:
            self.state_data['next_task'] = next_task
        
        self._save_state()
    
    def add_blocker(self, description: str) -> None:
        """Add a project blocker"""
        blocker = {
            "description": description,
            "created": datetime.utcnow().isoformat() + 'Z',
            "resolved": False
        }
        self.state_data['blockers'].append(blocker)
        self._save_state()
    
    def resolve_blocker(self, description: str) -> bool:
        """Resolve a project blocker"""
        for blocker in self.state_data['blockers']:
            if blocker['description'] == description and not blocker['resolved']:
                blocker['resolved'] = True
                blocker['resolved_date'] = datetime.utcnow().isoformat() + 'Z'
                self._save_state()
                return True
        return False
    
    def add_note(self, note: str) -> None:
        """Add a project note"""
        note_entry = {
            "content": note,
            "timestamp": datetime.utcnow().isoformat() + 'Z'
        }
        self.state_data['notes'].append(note_entry)
        self._save_state()
    
    def status_report(self) -> str:
        """Generate a status report"""
        return f"""
Project Status Report
====================
Sprint: {self.state_data.get('current_sprint', 'Unknown')}
Phase: {self.state_data.get('current_phase', 'Unknown')}
Progress: {self.state_data.get('sprint_progress', 0)*100:.1f}%

Current Task: {self.state_data.get('current_task', 'None')}
Next Task: {self.state_data.get('next_task', 'None')}

Active Blockers: {len([b for b in self.state_data.get('blockers', []) if not b.get('resolved', False)])}
Completed Tasks: {len(self.state_data.get('completed_tasks', []))}
"""
    
    def sync_all(self) -> None:
        """Synchronize all state with markdown files"""
        self._save_state()
        self._update_markdown_context()
    
    def _update_markdown_context(self) -> bool:
        """Update the CURRENT SPRINT CONTEXT section in markdown"""
        if not self.rules_file.exists():
            print(f"Warning: {self.rules_file} not found")
            return False
        
        with open(self.rules_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Generate new context section
        active_files_str = '\n'.join([f"- `{f}` - {desc}" for f, desc in zip(
            self.state_data.get('active_files', []),
            ['Implementation in progress'] * len(self.state_data.get('active_files', []))
        )])
        
        new_context = f"""**Current Sprint:** [{self.state_data.get('current_sprint', 'Unknown')}]
**Current Phase:** [{self.state_data.get('current_phase', 'Unknown')}]
**Last Completed:** [{self.state_data.get('last_completed', 'None')}]
**Current Task:** [{self.state_data.get('current_task', 'None')}]
**Next Task:** [{self.state_data.get('next_task', 'None')}]

**Active Files:**
{active_files_str}"""
        
        # Replace the context section
        pattern = r'(\*\*Current Sprint:\*\*.*?)(\*\*Key Decisions Made:\*\*)'
        
        def replace_context(match):
            return new_context + '\n\n**Key Decisions Made:**'
        
        updated_content = re.sub(pattern, replace_context, content, flags=re.DOTALL)
        
        if updated_content != content:
            with open(self.rules_file, 'w', encoding='utf-8') as f:
                f.write(updated_content)
            return True
        
        print("Warning: Could not find CURRENT SPRINT CONTEXT section to update")
        return False

    def update_sprint_progress(self, progress: float) -> None:
        """Update sprint progress percentage (0.0 to 1.0)"""
        self.state_data['sprint_progress'] = max(0.0, min(1.0, progress))
        self._save_state()

    def complete_gate(self, sprint: str, gate_description: str) -> bool:
        """Mark a sprint gate as completed"""
        gates = self.state_data.get('sprint_gates', {}).get(sprint, {}).get('gates', [])
        
        for gate in gates:
            if gate['description'] == gate_description:
                gate['completed'] = True
                gate['completion_date'] = datetime.utcnow().isoformat() + 'Z'
                
                # Update completed gates count
                sprint_data = self.state_data['sprint_gates'][sprint]
                sprint_data['completed_gates'] = sum(1 for g in gates if g['completed'])
                
                self._save_state()
                return True
        
        return False

    def check_sprint_completion(self) -> bool:
        """Check if current sprint is complete and should advance"""
        current_sprint = self.state_data.get('current_sprint', '')
        sprint_key = current_sprint.split(' - ')[0]
        
        if sprint_key in self.state_data.get('sprint_gates', {}):
            sprint_data = self.state_data['sprint_gates'][sprint_key]
            return sprint_data['completed_gates'] >= sprint_data['total_gates']
        
        return False

    def advance_sprint(self) -> bool:
        """Advance to next sprint if current is complete"""
        # Implementation would depend on sprint progression logic
        return False


def main():
    parser = argparse.ArgumentParser(description="Update project context and sync with markdown")
    parser.add_argument('--task', help='Mark task as completed')
    parser.add_argument('--current', help='Set current task')
    parser.add_argument('--next', help='Set next task')
    parser.add_argument('--progress', type=float, help='Set sprint progress (0.0-1.0)')
    parser.add_argument('--gate', help='Mark sprint gate as completed')
    parser.add_argument('--blocker', help='Add a blocker')
    parser.add_argument('--resolve', help='Resolve a blocker')
    parser.add_argument('--note', help='Add a note')
    parser.add_argument('--status', action='store_true', help='Show status report')
    parser.add_argument('--sync', action='store_true', help='Sync state with markdown')
    
    args = parser.parse_args()
    
    updater = ContextUpdater()
    
    if args.task or args.current or args.next:
        updater.update_task_progress(args.task, args.current, args.next)
    
    if args.progress is not None:
        updater.update_sprint_progress(args.progress)
    
    if args.gate:
        current_sprint = updater.state_data.get('current_sprint', '')
        sprint_key = current_sprint.split(' - ')[0]
        if updater.complete_gate(sprint_key, args.gate):
            print(f"✓ Completed gate: {args.gate}")
        else:
            print(f"✗ Gate not found: {args.gate}")
    
    if args.blocker:
        updater.add_blocker(args.blocker)
        print(f"Added blocker: {args.blocker}")
    
    if args.resolve:
        if updater.resolve_blocker(args.resolve):
            print(f"✓ Resolved blocker: {args.resolve}")
        else:
            print(f"✗ Blocker not found: {args.resolve}")
    
    if args.note:
        updater.add_note(args.note)
        print(f"Added note: {args.note}")
    
    if args.status:
        print(updater.status_report())
    
    if args.sync or any([args.task, args.current, args.next, args.progress, args.gate]):
        updater.sync_all()
    
    # Check for sprint completion
    if updater.check_sprint_completion():
        print("🎉 Current sprint is complete!")
        if updater.advance_sprint():
            print("🚀 Advanced to next sprint!")
            updater.sync_all()


if __name__ == "__main__":
    main()
