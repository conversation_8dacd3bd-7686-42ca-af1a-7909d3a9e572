"""
Audio Analysis for AI Music Organization Assistant

This module implements the AudioAnalyzer class that uses librosa to analyze
audio characteristics like tempo, key, energy levels, and spectral features.
This provides the AI's "listening" capability to understand music.

Author: AI Music Organization Assistant Project
"""

import logging
import numpy as np
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple, Union
from dataclasses import dataclass, asdict
import json
from datetime import datetime

import librosa
import librosa.display
from scipy import stats

from .config import AUDIO_ANALYSIS_CONFIG


@dataclass
class AudioFeatures:
    """
    Container for extracted audio features.
    
    This class provides a unified interface for all audio analysis results,
    making it easy to store and retrieve audio characteristics.
    """
    # File information
    file_path: str
    duration: float
    sample_rate: int
    
    # Tempo and rhythm
    tempo: Optional[float] = None
    tempo_confidence: Optional[float] = None
    beat_frames: Optional[List[int]] = None
    
    # Harmonic analysis
    key: Optional[str] = None
    key_confidence: Optional[float] = None
    chroma_features: Optional[List[float]] = None
    
    # Energy and dynamics
    energy_level: Optional[str] = None  # 'low', 'medium', 'high'
    rms_energy: Optional[float] = None
    spectral_centroid: Optional[float] = None
    spectral_rolloff: Optional[float] = None
    zero_crossing_rate: Optional[float] = None
    
    # Spectral features
    mfcc_features: Optional[List[float]] = None  # First 13 MFCC coefficients
    spectral_contrast: Optional[List[float]] = None
    tonnetz_features: Optional[List[float]] = None
    
    # Structural analysis
    onset_frames: Optional[List[int]] = None
    onset_strength: Optional[float] = None
    
    # Analysis metadata
    analysis_time: str = ""
    analysis_errors: List[str] = None
    
    def __post_init__(self):
        if self.analysis_errors is None:
            self.analysis_errors = []
        if not self.analysis_time:
            self.analysis_time = datetime.now().isoformat()


class AudioAnalyzer:
    """
    Analyzes audio files to extract musical features and characteristics.
    
    This class uses librosa to perform comprehensive audio analysis including:
    - Tempo detection and beat tracking
    - Key detection and harmonic analysis
    - Energy level classification
    - Spectral feature extraction
    - Structural analysis (onsets, etc.)
    
    The analyzer provides the AI's ability to "listen" to music and understand
    its characteristics, which is crucial for learning organizational patterns
    based on musical similarity.
    
    Features:
    - Professional-grade audio analysis using librosa
    - Robust error handling for various audio formats
    - Configurable analysis parameters
    - Comprehensive feature extraction
    - Performance optimization for large collections
    
    Usage:
        analyzer = AudioAnalyzer()
        features = analyzer.analyze_audio("/path/to/audio/file.mp3")
        if features:
            print(f"Tempo: {features.tempo} BPM")
            print(f"Key: {features.key}")
            print(f"Energy: {features.energy_level}")
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """
        Initialize the AudioAnalyzer.
        
        Args:
            config: Optional configuration dictionary (uses default if None)
        """
        self.config = config or AUDIO_ANALYSIS_CONFIG
        self.logger = self._setup_logging()
        
        # Key detection setup
        self.key_names = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B']
        self.mode_names = ['major', 'minor']
        
        # Statistics tracking
        self.stats = {
            'total_analyses': 0,
            'successful_analyses': 0,
            'failed_analyses': 0,
            'average_analysis_time': 0.0,
            'last_analysis_time': None
        }
    
    def _setup_logging(self) -> logging.Logger:
        """Set up logging for the audio analyzer."""
        logger = logging.getLogger(__name__)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def analyze_audio(self, file_path: Union[str, Path]) -> Optional[AudioFeatures]:
        """
        Perform comprehensive audio analysis on a file.
        
        Args:
            file_path: Path to the audio file
            
        Returns:
            AudioFeatures object if successful, None if analysis failed
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            self.logger.error(f"Audio file not found: {file_path}")
            return None
        
        self.logger.debug(f"Analyzing audio file: {file_path}")
        start_time = datetime.now()
        
        try:
            self.stats['total_analyses'] += 1
            
            # Load audio file with error handling for various formats
            try:
                y, sr = librosa.load(
                    file_path,
                    sr=self.config['sample_rate'],
                    duration=None  # Load entire file
                )
            except Exception as load_error:
                # Try loading with different parameters for problematic files
                try:
                    y, sr = librosa.load(
                        file_path,
                        sr=None,  # Use original sample rate
                        duration=30  # Limit to 30 seconds for analysis
                    )
                except Exception:
                    raise load_error
            
            duration = librosa.get_duration(y=y, sr=sr)
            
            # Initialize features object
            features = AudioFeatures(
                file_path=str(file_path),
                duration=duration,
                sample_rate=sr
            )
            
            # Perform various analyses
            self._analyze_tempo_and_beats(y, sr, features)
            self._analyze_key_and_harmony(y, sr, features)
            self._analyze_energy_and_dynamics(y, sr, features)
            self._analyze_spectral_features(y, sr, features)
            self._analyze_structure(y, sr, features)
            
            # Calculate analysis time
            analysis_duration = (datetime.now() - start_time).total_seconds()
            self.stats['average_analysis_time'] = (
                (self.stats['average_analysis_time'] * (self.stats['total_analyses'] - 1) + analysis_duration) 
                / self.stats['total_analyses']
            )
            
            self.stats['successful_analyses'] += 1
            self.stats['last_analysis_time'] = datetime.now().isoformat()
            
            self.logger.debug(f"Audio analysis completed in {analysis_duration:.2f}s: {file_path}")
            return features
            
        except Exception as e:
            self.stats['failed_analyses'] += 1
            error_msg = f"Audio analysis failed for {file_path}: {e}"
            self.logger.error(error_msg)
            
            # Return basic features with error
            return AudioFeatures(
                file_path=str(file_path),
                duration=0.0,
                sample_rate=self.config['sample_rate'],
                analysis_errors=[error_msg]
            )
    
    def _analyze_tempo_and_beats(self, y: np.ndarray, sr: int, features: AudioFeatures) -> None:
        """Analyze tempo and beat information."""
        try:
            # Tempo detection
            tempo, beats = librosa.beat.beat_track(
                y=y, 
                sr=sr,
                hop_length=self.config['hop_length']
            )
            
            features.tempo = float(tempo)
            features.beat_frames = beats.tolist() if len(beats) > 0 else []
            
            # Calculate tempo confidence based on beat consistency
            if len(beats) > 1:
                beat_intervals = np.diff(beats)
                tempo_consistency = 1.0 - (np.std(beat_intervals) / np.mean(beat_intervals))
                features.tempo_confidence = max(0.0, min(1.0, tempo_consistency))
            else:
                features.tempo_confidence = 0.0
            
            self.logger.debug(f"Tempo: {features.tempo:.1f} BPM (confidence: {features.tempo_confidence:.2f})")
            
        except Exception as e:
            features.analysis_errors.append(f"Tempo analysis failed: {e}")
            self.logger.warning(f"Tempo analysis failed: {e}")
    
    def _analyze_key_and_harmony(self, y: np.ndarray, sr: int, features: AudioFeatures) -> None:
        """Analyze key and harmonic content."""
        try:
            # Extract chroma features
            chroma = librosa.feature.chroma_stft(
                y=y, 
                sr=sr,
                hop_length=self.config['hop_length']
            )
            
            # Average chroma across time
            chroma_mean = np.mean(chroma, axis=1)
            features.chroma_features = chroma_mean.tolist()
            
            # Simple key detection using chroma profile matching
            # This is a basic implementation - could be enhanced with more sophisticated algorithms
            key_profiles = self._get_key_profiles()
            
            correlations = []
            for profile in key_profiles:
                correlation = np.corrcoef(chroma_mean, profile)[0, 1]
                correlations.append(correlation if not np.isnan(correlation) else 0.0)
            
            best_key_idx = np.argmax(correlations)
            features.key_confidence = float(correlations[best_key_idx])
            
            # Convert index to key name
            key_idx = best_key_idx % 12
            mode_idx = best_key_idx // 12
            features.key = f"{self.key_names[key_idx]} {self.mode_names[mode_idx]}"
            
            self.logger.debug(f"Key: {features.key} (confidence: {features.key_confidence:.2f})")
            
        except Exception as e:
            features.analysis_errors.append(f"Key analysis failed: {e}")
            self.logger.warning(f"Key analysis failed: {e}")
    
    def _analyze_energy_and_dynamics(self, y: np.ndarray, sr: int, features: AudioFeatures) -> None:
        """Analyze energy levels and dynamic characteristics."""
        try:
            # RMS energy
            rms = librosa.feature.rms(
                y=y,
                hop_length=self.config['hop_length']
            )[0]
            features.rms_energy = float(np.mean(rms))
            
            # Spectral centroid (brightness)
            spectral_centroids = librosa.feature.spectral_centroid(
                y=y, 
                sr=sr,
                hop_length=self.config['hop_length']
            )[0]
            features.spectral_centroid = float(np.mean(spectral_centroids))
            
            # Spectral rolloff
            spectral_rolloff = librosa.feature.spectral_rolloff(
                y=y, 
                sr=sr,
                hop_length=self.config['hop_length']
            )[0]
            features.spectral_rolloff = float(np.mean(spectral_rolloff))
            
            # Zero crossing rate
            zcr = librosa.feature.zero_crossing_rate(
                y,
                hop_length=self.config['hop_length']
            )[0]
            features.zero_crossing_rate = float(np.mean(zcr))
            
            # Classify energy level
            energy_percentiles = np.percentile(rms, [33, 67])
            if features.rms_energy < energy_percentiles[0]:
                features.energy_level = "low"
            elif features.rms_energy < energy_percentiles[1]:
                features.energy_level = "medium"
            else:
                features.energy_level = "high"
            
            self.logger.debug(f"Energy: {features.energy_level} (RMS: {features.rms_energy:.4f})")
            
        except Exception as e:
            features.analysis_errors.append(f"Energy analysis failed: {e}")
            self.logger.warning(f"Energy analysis failed: {e}")
    
    def _analyze_spectral_features(self, y: np.ndarray, sr: int, features: AudioFeatures) -> None:
        """Extract spectral features for similarity analysis."""
        try:
            # MFCC features (first 13 coefficients)
            mfccs = librosa.feature.mfcc(
                y=y, 
                sr=sr,
                n_mfcc=13,
                hop_length=self.config['hop_length']
            )
            features.mfcc_features = np.mean(mfccs, axis=1).tolist()
            
            # Spectral contrast
            spectral_contrast = librosa.feature.spectral_contrast(
                y=y, 
                sr=sr,
                hop_length=self.config['hop_length']
            )
            features.spectral_contrast = np.mean(spectral_contrast, axis=1).tolist()
            
            # Tonnetz features (harmonic network)
            tonnetz = librosa.feature.tonnetz(
                y=librosa.effects.harmonic(y), 
                sr=sr
            )
            features.tonnetz_features = np.mean(tonnetz, axis=1).tolist()
            
            self.logger.debug("Spectral features extracted successfully")
            
        except Exception as e:
            features.analysis_errors.append(f"Spectral analysis failed: {e}")
            self.logger.warning(f"Spectral analysis failed: {e}")
    
    def _analyze_structure(self, y: np.ndarray, sr: int, features: AudioFeatures) -> None:
        """Analyze structural elements like onsets."""
        try:
            # Onset detection
            onset_frames = librosa.onset.onset_detect(
                y=y, 
                sr=sr,
                hop_length=self.config['hop_length']
            )
            features.onset_frames = onset_frames.tolist()
            
            # Onset strength
            onset_envelope = librosa.onset.onset_strength(
                y=y, 
                sr=sr,
                hop_length=self.config['hop_length']
            )
            features.onset_strength = float(np.mean(onset_envelope))
            
            self.logger.debug(f"Structure: {len(onset_frames)} onsets detected")
            
        except Exception as e:
            features.analysis_errors.append(f"Structure analysis failed: {e}")
            self.logger.warning(f"Structure analysis failed: {e}")
    
    def _get_key_profiles(self) -> List[np.ndarray]:
        """Get key profiles for key detection."""
        # Krumhansl-Schmuckler key profiles
        major_profile = np.array([6.35, 2.23, 3.48, 2.33, 4.38, 4.09, 2.52, 5.19, 2.39, 3.66, 2.29, 2.88])
        minor_profile = np.array([6.33, 2.68, 3.52, 5.38, 2.60, 3.53, 2.54, 4.75, 3.98, 2.69, 3.34, 3.17])
        
        # Normalize profiles
        major_profile = major_profile / np.sum(major_profile)
        minor_profile = minor_profile / np.sum(minor_profile)
        
        # Generate all 24 key profiles (12 major + 12 minor)
        profiles = []
        
        # Major keys
        for i in range(12):
            profile = np.roll(major_profile, i)
            profiles.append(profile)
        
        # Minor keys
        for i in range(12):
            profile = np.roll(minor_profile, i)
            profiles.append(profile)
        
        return profiles
    
    def get_stats(self) -> Dict[str, Any]:
        """Get audio analysis statistics."""
        return self.stats.copy()
    
    def to_dict(self, features: AudioFeatures) -> Dict[str, Any]:
        """Convert AudioFeatures to dictionary."""
        return asdict(features)
    
    def to_json(self, features: AudioFeatures) -> str:
        """Convert AudioFeatures to JSON string."""
        return json.dumps(self.to_dict(features), indent=2, ensure_ascii=False)


# Example usage and testing
if __name__ == "__main__":
    # Example usage
    analyzer = AudioAnalyzer()
    
    # This would analyze an audio file - replace with actual path for testing
    # features = analyzer.analyze_audio("/path/to/audio/file.mp3")
    # if features:
    #     print(f"Tempo: {features.tempo} BPM")
    #     print(f"Key: {features.key}")
    #     print(f"Energy: {features.energy_level}")
    #     print(f"Analysis stats: {analyzer.get_stats()}")
    
    print("AudioAnalyzer initialized successfully")
