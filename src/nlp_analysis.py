"""
Natural Language Processing Analysis for AI Music Organization Assistant

This module implements the CommentAnalyzer class that uses spaCy for natural
language processing of comment fields to extract user vocabulary and
descriptive patterns. This helps understand the user's descriptive style.

Author: AI Music Organization Assistant Project
"""

import logging
import re
from typing import Dict, List, Optional, Set, Tuple, Any
from dataclasses import dataclass, asdict
from collections import Counter, defaultdict
import json
from datetime import datetime

import spacy
from spacy.lang.en.stop_words import STOP_WORDS

from .config import PATTERN_CONFIG


@dataclass
class VocabularyPattern:
    """Represents a detected vocabulary pattern."""
    word: str
    frequency: int
    context_types: List[str]  # 'descriptive', 'emotional', 'technical', etc.
    example_phrases: List[str]
    confidence: float
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class StylePattern:
    """Represents a detected writing style pattern."""
    pattern_type: str  # 'length', 'formality', 'emotion', 'structure'
    pattern_name: str
    pattern_description: str
    frequency: int
    confidence: float
    examples: List[str]
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class CommentAnalysis:
    """Complete analysis results for comment fields."""
    total_comments: int
    vocabulary_patterns: List[VocabularyPattern]
    style_patterns: List[StylePattern]
    common_phrases: List[Tuple[str, int]]
    sentiment_distribution: Dict[str, float]
    avg_comment_length: float
    language_detected: str
    analysis_time: str
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


class CommentAnalyzer:
    """
    Analyzes comment fields using natural language processing.
    
    This class uses spaCy to understand how users describe their music,
    extracting vocabulary patterns, writing styles, and descriptive
    preferences. This helps the AI learn to generate comments that
    match the user's personal style.
    
    Features:
    - Vocabulary extraction and categorization
    - Writing style analysis
    - Phrase pattern detection
    - Sentiment analysis
    - Language detection
    - Comment length and structure analysis
    
    Usage:
        analyzer = CommentAnalyzer()
        comments = ["Great energy track", "Chill ambient piece", ...]
        analysis = analyzer.analyze_comments(comments)
        print(f"Common vocabulary: {analysis.vocabulary_patterns}")
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """
        Initialize the CommentAnalyzer.
        
        Args:
            config: Optional configuration dictionary
        """
        self.config = config or PATTERN_CONFIG
        self.logger = self._setup_logging()
        
        # Load spaCy model
        try:
            self.nlp = spacy.load("en_core_web_sm")
        except OSError:
            self.logger.error("spaCy English model not found. Please install with: python -m spacy download en_core_web_sm")
            raise
        
        # Music-specific vocabulary categories
        self.music_categories = {
            'energy': ['energetic', 'high-energy', 'pumping', 'driving', 'intense', 'powerful', 'dynamic', 'explosive'],
            'mood': ['happy', 'sad', 'melancholic', 'uplifting', 'dark', 'bright', 'moody', 'cheerful', 'somber'],
            'tempo': ['fast', 'slow', 'quick', 'rapid', 'leisurely', 'brisk', 'moderate', 'steady'],
            'style': ['smooth', 'rough', 'polished', 'raw', 'clean', 'dirty', 'crisp', 'warm', 'cold'],
            'genre_descriptors': ['rock', 'pop', 'jazz', 'classical', 'electronic', 'ambient', 'techno', 'house'],
            'quality': ['excellent', 'good', 'great', 'amazing', 'poor', 'bad', 'mediocre', 'outstanding'],
            'technical': ['bass', 'treble', 'vocals', 'drums', 'guitar', 'synth', 'mix', 'production', 'mastering'],
            'emotional': ['love', 'hate', 'like', 'enjoy', 'feel', 'emotional', 'touching', 'moving', 'inspiring']
        }
        
        # Flatten vocabulary for quick lookup
        self.music_vocab = set()
        for category_words in self.music_categories.values():
            self.music_vocab.update(category_words)
        
        # Statistics tracking
        self.stats = {
            'comments_analyzed': 0,
            'vocabulary_extracted': 0,
            'patterns_detected': 0,
            'last_analysis_time': None
        }
    
    def _setup_logging(self) -> logging.Logger:
        """Set up logging for the comment analyzer."""
        logger = logging.getLogger(__name__)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def analyze_comments(self, comments: List[str]) -> CommentAnalysis:
        """
        Analyze a collection of comment strings.
        
        Args:
            comments: List of comment strings to analyze
            
        Returns:
            CommentAnalysis object with extracted patterns
        """
        if not comments:
            return CommentAnalysis(
                total_comments=0,
                vocabulary_patterns=[],
                style_patterns=[],
                common_phrases=[],
                sentiment_distribution={},
                avg_comment_length=0.0,
                language_detected='en',
                analysis_time=datetime.now().isoformat()
            )
        
        self.logger.info(f"Analyzing {len(comments)} comments")
        start_time = datetime.now()
        
        try:
            self.stats['comments_analyzed'] += len(comments)
            
            # Clean and preprocess comments
            cleaned_comments = self._preprocess_comments(comments)
            
            # Extract vocabulary patterns
            vocabulary_patterns = self._extract_vocabulary_patterns(cleaned_comments)
            
            # Analyze writing style
            style_patterns = self._analyze_writing_style(cleaned_comments)
            
            # Extract common phrases
            common_phrases = self._extract_common_phrases(cleaned_comments)
            
            # Analyze sentiment
            sentiment_distribution = self._analyze_sentiment(cleaned_comments)
            
            # Calculate statistics
            avg_length = sum(len(comment) for comment in cleaned_comments) / len(cleaned_comments)
            
            # Create analysis result
            analysis = CommentAnalysis(
                total_comments=len(comments),
                vocabulary_patterns=vocabulary_patterns,
                style_patterns=style_patterns,
                common_phrases=common_phrases,
                sentiment_distribution=sentiment_distribution,
                avg_comment_length=avg_length,
                language_detected='en',  # Could be enhanced with language detection
                analysis_time=datetime.now().isoformat()
            )
            
            # Update statistics
            self.stats['vocabulary_extracted'] += len(vocabulary_patterns)
            self.stats['patterns_detected'] += len(style_patterns)
            self.stats['last_analysis_time'] = datetime.now().isoformat()
            
            analysis_time = (datetime.now() - start_time).total_seconds()
            self.logger.info(f"Comment analysis completed in {analysis_time:.2f}s")
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"Comment analysis failed: {e}")
            return CommentAnalysis(
                total_comments=len(comments),
                vocabulary_patterns=[],
                style_patterns=[],
                common_phrases=[],
                sentiment_distribution={},
                avg_comment_length=0.0,
                language_detected='en',
                analysis_time=datetime.now().isoformat()
            )
    
    def _preprocess_comments(self, comments: List[str]) -> List[str]:
        """Clean and preprocess comment strings."""
        cleaned = []
        
        for comment in comments:
            if not comment or not isinstance(comment, str):
                continue
            
            # Basic cleaning
            comment = comment.strip()
            if not comment:
                continue
            
            # Remove excessive whitespace
            comment = re.sub(r'\s+', ' ', comment)
            
            # Remove special characters but keep basic punctuation
            comment = re.sub(r'[^\w\s\-.,!?()\'"]', '', comment)
            
            cleaned.append(comment)
        
        return cleaned
    
    def _extract_vocabulary_patterns(self, comments: List[str]) -> List[VocabularyPattern]:
        """Extract vocabulary patterns from comments."""
        vocabulary_patterns = []
        
        try:
            # Process all comments with spaCy
            all_words = []
            word_contexts = defaultdict(list)
            
            for comment in comments:
                doc = self.nlp(comment.lower())
                
                for token in doc:
                    # Skip stop words, punctuation, and very short words
                    if (token.is_stop or token.is_punct or token.is_space or 
                        len(token.text) < 3 or token.text in STOP_WORDS):
                        continue
                    
                    # Use lemmatized form
                    word = token.lemma_
                    all_words.append(word)
                    word_contexts[word].append(comment)
            
            # Count word frequencies
            word_counts = Counter(all_words)
            
            # Create vocabulary patterns for significant words
            min_frequency = max(2, self.config.get('vocabulary_min_frequency', 2))
            
            for word, frequency in word_counts.most_common():
                if frequency < min_frequency:
                    break
                
                # Determine context types
                context_types = self._categorize_word(word)
                
                # Calculate confidence based on frequency and context
                confidence = min(1.0, frequency / len(comments))
                
                # Get example phrases
                example_phrases = word_contexts[word][:3]
                
                pattern = VocabularyPattern(
                    word=word,
                    frequency=frequency,
                    context_types=context_types,
                    example_phrases=example_phrases,
                    confidence=confidence
                )
                
                vocabulary_patterns.append(pattern)
            
            # Limit to top patterns
            vocabulary_patterns = vocabulary_patterns[:50]
            
        except Exception as e:
            self.logger.error(f"Error extracting vocabulary patterns: {e}")
        
        return vocabulary_patterns
    
    def _categorize_word(self, word: str) -> List[str]:
        """Categorize a word based on music-related contexts."""
        categories = []
        
        for category, words in self.music_categories.items():
            if word in words or any(word in w or w in word for w in words):
                categories.append(category)
        
        # If no specific category, try to infer from word properties
        if not categories:
            # Check if it's likely a descriptive adjective
            doc = self.nlp(word)
            if doc and doc[0].pos_ == 'ADJ':
                categories.append('descriptive')
            elif doc and doc[0].pos_ == 'NOUN':
                categories.append('technical')
            elif doc and doc[0].pos_ == 'VERB':
                categories.append('action')
            else:
                categories.append('general')
        
        return categories
    
    def _analyze_writing_style(self, comments: List[str]) -> List[StylePattern]:
        """Analyze writing style patterns in comments."""
        style_patterns = []
        
        try:
            if not comments:
                return style_patterns
            
            # Analyze comment length patterns
            lengths = [len(comment) for comment in comments]
            avg_length = sum(lengths) / len(lengths)
            
            if avg_length < 20:
                style_patterns.append(StylePattern(
                    pattern_type='length',
                    pattern_name='concise_style',
                    pattern_description='Prefers short, concise comments',
                    frequency=len([l for l in lengths if l < 20]),
                    confidence=len([l for l in lengths if l < 20]) / len(lengths),
                    examples=[c for c in comments if len(c) < 20][:3]
                ))
            elif avg_length > 50:
                style_patterns.append(StylePattern(
                    pattern_type='length',
                    pattern_name='detailed_style',
                    pattern_description='Prefers detailed, descriptive comments',
                    frequency=len([l for l in lengths if l > 50]),
                    confidence=len([l for l in lengths if l > 50]) / len(lengths),
                    examples=[c for c in comments if len(c) > 50][:3]
                ))
            
            # Analyze punctuation usage
            exclamation_count = sum(1 for comment in comments if '!' in comment)
            if exclamation_count > len(comments) * 0.3:
                style_patterns.append(StylePattern(
                    pattern_type='emotion',
                    pattern_name='enthusiastic_style',
                    pattern_description='Uses exclamation marks frequently',
                    frequency=exclamation_count,
                    confidence=exclamation_count / len(comments),
                    examples=[c for c in comments if '!' in c][:3]
                ))
            
            # Analyze capitalization patterns
            all_caps_count = sum(1 for comment in comments if comment.isupper() and len(comment) > 3)
            if all_caps_count > 0:
                style_patterns.append(StylePattern(
                    pattern_type='formality',
                    pattern_name='emphatic_caps',
                    pattern_description='Uses ALL CAPS for emphasis',
                    frequency=all_caps_count,
                    confidence=all_caps_count / len(comments),
                    examples=[c for c in comments if c.isupper() and len(c) > 3][:3]
                ))
            
            # Analyze sentence structure
            multi_sentence_count = sum(1 for comment in comments if len(comment.split('.')) > 1)
            if multi_sentence_count > len(comments) * 0.3:
                style_patterns.append(StylePattern(
                    pattern_type='structure',
                    pattern_name='complex_structure',
                    pattern_description='Uses multiple sentences in comments',
                    frequency=multi_sentence_count,
                    confidence=multi_sentence_count / len(comments),
                    examples=[c for c in comments if len(c.split('.')) > 1][:3]
                ))
            
        except Exception as e:
            self.logger.error(f"Error analyzing writing style: {e}")
        
        return style_patterns
    
    def _extract_common_phrases(self, comments: List[str]) -> List[Tuple[str, int]]:
        """Extract common phrases from comments."""
        phrases = []
        
        try:
            # Extract 2-3 word phrases
            all_phrases = []
            
            for comment in comments:
                doc = self.nlp(comment.lower())
                
                # Extract 2-word phrases
                for i in range(len(doc) - 1):
                    if not doc[i].is_stop and not doc[i].is_punct and not doc[i+1].is_stop and not doc[i+1].is_punct:
                        phrase = f"{doc[i].lemma_} {doc[i+1].lemma_}"
                        all_phrases.append(phrase)
                
                # Extract 3-word phrases
                for i in range(len(doc) - 2):
                    if (not doc[i].is_stop and not doc[i].is_punct and 
                        not doc[i+1].is_stop and not doc[i+1].is_punct and
                        not doc[i+2].is_stop and not doc[i+2].is_punct):
                        phrase = f"{doc[i].lemma_} {doc[i+1].lemma_} {doc[i+2].lemma_}"
                        all_phrases.append(phrase)
            
            # Count phrase frequencies
            phrase_counts = Counter(all_phrases)
            
            # Return top phrases with minimum frequency
            min_frequency = max(2, self.config.get('vocabulary_min_frequency', 2))
            phrases = [(phrase, count) for phrase, count in phrase_counts.most_common(20) 
                      if count >= min_frequency]
            
        except Exception as e:
            self.logger.error(f"Error extracting common phrases: {e}")
        
        return phrases
    
    def _analyze_sentiment(self, comments: List[str]) -> Dict[str, float]:
        """Analyze sentiment distribution in comments."""
        sentiment_distribution = {'positive': 0.0, 'neutral': 0.0, 'negative': 0.0}
        
        try:
            if not comments:
                return sentiment_distribution
            
            # Simple sentiment analysis using word lists
            positive_words = {'good', 'great', 'excellent', 'amazing', 'awesome', 'love', 'like', 'enjoy', 'fantastic', 'wonderful'}
            negative_words = {'bad', 'terrible', 'awful', 'hate', 'dislike', 'poor', 'horrible', 'worst', 'annoying'}
            
            sentiment_scores = []
            
            for comment in comments:
                doc = self.nlp(comment.lower())
                positive_count = sum(1 for token in doc if token.lemma_ in positive_words)
                negative_count = sum(1 for token in doc if token.lemma_ in negative_words)
                
                if positive_count > negative_count:
                    sentiment_scores.append('positive')
                elif negative_count > positive_count:
                    sentiment_scores.append('negative')
                else:
                    sentiment_scores.append('neutral')
            
            # Calculate distribution
            total = len(sentiment_scores)
            sentiment_distribution = {
                'positive': sentiment_scores.count('positive') / total,
                'neutral': sentiment_scores.count('neutral') / total,
                'negative': sentiment_scores.count('negative') / total
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing sentiment: {e}")
        
        return sentiment_distribution
    
    def generate_comment_suggestions(self, analysis: CommentAnalysis, 
                                   context: Dict[str, Any]) -> List[str]:
        """Generate comment suggestions based on learned patterns."""
        suggestions = []
        
        try:
            # Use top vocabulary and style patterns to generate suggestions
            top_words = [vp.word for vp in analysis.vocabulary_patterns[:10]]
            
            # Simple template-based generation
            templates = [
                "{adjective} {genre} track",
                "{quality} {style} piece",
                "{mood} and {energy}",
                "{technical} sounds {quality}"
            ]
            
            # Fill templates with learned vocabulary
            for template in templates:
                try:
                    # Simple word selection based on categories
                    adjective = next((vp.word for vp in analysis.vocabulary_patterns 
                                    if 'descriptive' in vp.context_types), 'good')
                    genre = next((vp.word for vp in analysis.vocabulary_patterns 
                                if 'genre_descriptors' in vp.context_types), 'music')
                    quality = next((vp.word for vp in analysis.vocabulary_patterns 
                                  if 'quality' in vp.context_types), 'nice')
                    style = next((vp.word for vp in analysis.vocabulary_patterns 
                                if 'style' in vp.context_types), 'smooth')
                    mood = next((vp.word for vp in analysis.vocabulary_patterns 
                               if 'mood' in vp.context_types), 'happy')
                    energy = next((vp.word for vp in analysis.vocabulary_patterns 
                                 if 'energy' in vp.context_types), 'energetic')
                    technical = next((vp.word for vp in analysis.vocabulary_patterns 
                                    if 'technical' in vp.context_types), 'mix')
                    
                    suggestion = template.format(
                        adjective=adjective,
                        genre=genre,
                        quality=quality,
                        style=style,
                        mood=mood,
                        energy=energy,
                        technical=technical
                    )
                    
                    if suggestion not in suggestions:
                        suggestions.append(suggestion)
                        
                except (StopIteration, KeyError):
                    continue
            
            # Limit suggestions
            suggestions = suggestions[:5]
            
        except Exception as e:
            self.logger.error(f"Error generating comment suggestions: {e}")
        
        return suggestions
    
    def get_stats(self) -> Dict[str, Any]:
        """Get comment analysis statistics."""
        return self.stats.copy()


# Example usage and testing
if __name__ == "__main__":
    # Example usage
    analyzer = CommentAnalyzer()
    
    # Example comments
    example_comments = [
        "Great energy track with pumping bass",
        "Smooth jazz piece, very relaxing",
        "Love the ambient soundscape",
        "Excellent production quality",
        "Dark and moody electronic"
    ]
    
    analysis = analyzer.analyze_comments(example_comments)
    print(f"Vocabulary patterns: {len(analysis.vocabulary_patterns)}")
    print(f"Style patterns: {len(analysis.style_patterns)}")
    print(f"Common phrases: {analysis.common_phrases}")
    
    print("CommentAnalyzer initialized successfully")
