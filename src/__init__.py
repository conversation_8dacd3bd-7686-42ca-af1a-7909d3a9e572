"""
AI Music Organization Assistant

A personalized AI that learns your music organization style and automatically
applies it to new files. This package implements the core pipeline:
Scanner → Metadata → Patterns → Organization

Author: AI Music Organization Assistant Project
"""

__version__ = "0.1.0"
__author__ = "AI Music Organization Assistant Project"

# Import main classes for easy access
from .scanner import MusicScanner, ScanResult
from .metadata import MetadataExtractor, AudioMetadata
from .database import DatabaseManager
from .gui import MainWindow, ProgressWindow
from .config import get_config, SUPPORTED_AUDIO_FORMATS

__all__ = [
    'MusicScanner',
    'ScanResult',
    'MetadataExtractor',
    'AudioMetadata',
    'DatabaseManager',
    'MainWindow',
    'ProgressWindow',
    'get_config',
    'SUPPORTED_AUDIO_FORMATS'
]
