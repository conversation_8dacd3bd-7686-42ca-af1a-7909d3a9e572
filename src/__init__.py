"""
AI Music Organization Assistant

A personalized AI that learns your music organization style and automatically
applies it to new files. This package implements the core pipeline:
Scanner → Metadata → Patterns → Organization

Author: AI Music Organization Assistant Project
"""

__version__ = "0.1.0"
__author__ = "AI Music Organization Assistant Project"

# Import main classes for easy access
from .scanner import Music<PERSON>canner, ScanResult
from .metadata import MetadataExtractor, AudioMetadata
from .database import DatabaseManager
from .gui import MainWindow, ProgressWindow
from .audio_analysis import AudioAnalyzer, AudioFeatures
from .pattern_recognition import PatternRecognizer, OrganizationalPattern
from .nlp_analysis import CommentAnalyzer, CommentAnalysis
from .pattern_learning import Pattern<PERSON><PERSON>ner, PredictionResult
from .config import get_config, SUPPORTED_AUDIO_FORMATS

__all__ = [
    'MusicScanner',
    'ScanResult',
    'MetadataExtractor',
    'AudioMetadata',
    'DatabaseManager',
    'MainWindow',
    'ProgressWindow',
    'AudioAnalyzer',
    'AudioFeatures',
    'PatternRecognizer',
    'OrganizationalPattern',
    'CommentAnalyzer',
    'CommentAnalysis',
    'PatternLearner',
    'PredictionResult',
    'get_config',
    'SUPPORTED_AUDIO_FORMATS'
]
