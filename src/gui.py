"""
Basic GUI for AI Music Organization Assistant

This module implements a basic tkinter interface for directory selection and
progress display, ensuring no blocking operations on the main thread with
proper progress indicators.

Author: AI Music Organization Assistant Project
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import queue
import logging
from pathlib import Path
from typing import Optional, Callable, Dict, Any
from datetime import datetime

from .scanner import MusicScanner, ScanResult
from .metadata import MetadataExtractor, AudioMetadata
from .database import DatabaseManager
from .config import GUI_CONFIG


class ProgressWindow:
    """
    Progress window for long-running operations.
    
    This window shows progress bars, current file being processed,
    and allows cancellation of operations.
    """
    
    def __init__(self, parent, title: str = "Processing..."):
        self.parent = parent
        self.window = tk.Toplevel(parent)
        self.window.title(title)
        self.window.geometry("500x200")
        self.window.resizable(False, False)
        
        # Center the window
        self.window.transient(parent)
        self.window.grab_set()
        
        # Progress tracking
        self.cancelled = False
        self.current_operation = ""
        
        self._setup_ui()
    
    def _setup_ui(self):
        """Set up the progress window UI."""
        # Main frame
        main_frame = ttk.Frame(self.window, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Operation label
        self.operation_label = ttk.Label(main_frame, text="Initializing...")
        self.operation_label.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            main_frame, 
            variable=self.progress_var, 
            maximum=100,
            length=400
        )
        self.progress_bar.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Current file label
        self.file_label = ttk.Label(main_frame, text="", wraplength=400)
        self.file_label.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Statistics label
        self.stats_label = ttk.Label(main_frame, text="")
        self.stats_label.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Cancel button
        self.cancel_button = ttk.Button(main_frame, text="Cancel", command=self.cancel)
        self.cancel_button.grid(row=4, column=0, pady=(10, 0))
        
        # Close button (initially disabled)
        self.close_button = ttk.Button(main_frame, text="Close", command=self.close, state="disabled")
        self.close_button.grid(row=4, column=1, pady=(10, 0))
        
        # Configure grid weights
        main_frame.columnconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        self.window.columnconfigure(0, weight=1)
        self.window.rowconfigure(0, weight=1)
    
    def update_progress(self, current: int, total: int, current_file: str = "", operation: str = ""):
        """Update progress display."""
        if total > 0:
            percentage = (current / total) * 100
            self.progress_var.set(percentage)
        
        if operation:
            self.operation_label.config(text=operation)
        
        if current_file:
            file_name = Path(current_file).name
            self.file_label.config(text=f"Processing: {file_name}")
        
        self.stats_label.config(text=f"Progress: {current}/{total}")
        
        self.window.update_idletasks()
    
    def set_completed(self, message: str = "Operation completed"):
        """Mark operation as completed."""
        self.operation_label.config(text=message)
        self.progress_var.set(100)
        self.file_label.config(text="")
        self.cancel_button.config(state="disabled")
        self.close_button.config(state="normal")
    
    def cancel(self):
        """Cancel the operation."""
        self.cancelled = True
        self.operation_label.config(text="Cancelling...")
        self.cancel_button.config(state="disabled")
    
    def close(self):
        """Close the progress window."""
        self.window.destroy()


class MainWindow:
    """
    Main application window for the AI Music Organization Assistant.
    
    This class provides the primary interface for users to:
    - Select music directories to scan
    - View scan progress and results
    - Access basic organization features
    - View application logs and statistics
    
    Features:
    - Non-blocking operations using threading
    - Progress tracking for long operations
    - Error handling and user feedback
    - Basic logging display
    - Statistics and status information
    """
    
    def __init__(self):
        """Initialize the main window."""
        self.root = tk.Tk()
        self.root.title("AI Music Organization Assistant")
        self.root.geometry(f"{GUI_CONFIG['window_width']}x{GUI_CONFIG['window_height']}")
        
        # Initialize components
        self.scanner = MusicScanner()
        self.extractor = MetadataExtractor()
        self.database = DatabaseManager()
        
        # Initialize database
        if not self.database.initialize_database():
            messagebox.showerror("Error", "Failed to initialize database")
            return
        
        # Threading and communication
        self.message_queue = queue.Queue()
        self.current_operation = None
        
        # Setup logging
        self.logger = self._setup_logging()
        
        # Setup UI
        self._setup_ui()
        
        # Start message processing
        self._process_messages()
    
    def _setup_logging(self) -> logging.Logger:
        """Set up logging for the GUI."""
        logger = logging.getLogger(__name__)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def _setup_ui(self):
        """Set up the main window UI."""
        # Create main menu
        self._create_menu()
        
        # Create main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Directory selection frame
        dir_frame = ttk.LabelFrame(main_frame, text="Music Directory", padding="10")
        dir_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.directory_var = tk.StringVar()
        self.directory_entry = ttk.Entry(dir_frame, textvariable=self.directory_var, width=60)
        self.directory_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 10))
        
        self.browse_button = ttk.Button(dir_frame, text="Browse", command=self.browse_directory)
        self.browse_button.grid(row=0, column=1)
        
        self.scan_button = ttk.Button(dir_frame, text="Scan Directory", command=self.scan_directory)
        self.scan_button.grid(row=1, column=0, columnspan=2, pady=(10, 0))
        
        # Configure directory frame
        dir_frame.columnconfigure(0, weight=1)
        
        # Statistics frame
        stats_frame = ttk.LabelFrame(main_frame, text="Statistics", padding="10")
        stats_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))
        
        self.stats_text = scrolledtext.ScrolledText(stats_frame, height=15, width=40)
        self.stats_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Log frame
        log_frame = ttk.LabelFrame(main_frame, text="Activity Log", padding="10")
        log_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 0))
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, width=40)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        main_frame.columnconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        stats_frame.columnconfigure(0, weight=1)
        stats_frame.rowconfigure(0, weight=1)
        
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        
        # Initial statistics update
        self.update_statistics()
        self.log_message("Application started")
    
    def _create_menu(self):
        """Create the application menu."""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="Exit", command=self.root.quit)
        
        # Tools menu
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Tools", menu=tools_menu)
        tools_menu.add_command(label="Clear Cache", command=self.clear_cache)
        tools_menu.add_command(label="Backup Database", command=self.backup_database)
        
        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="About", command=self.show_about)
    
    def browse_directory(self):
        """Open directory browser dialog."""
        directory = filedialog.askdirectory(title="Select Music Directory")
        if directory:
            self.directory_var.set(directory)
    
    def scan_directory(self):
        """Start directory scanning in a separate thread."""
        directory = self.directory_var.get().strip()
        if not directory:
            messagebox.showwarning("Warning", "Please select a directory to scan")
            return
        
        if not Path(directory).exists():
            messagebox.showerror("Error", "Selected directory does not exist")
            return
        
        # Disable scan button during operation
        self.scan_button.config(state="disabled")
        
        # Start scanning in background thread
        thread = threading.Thread(target=self._scan_worker, args=(directory,))
        thread.daemon = True
        thread.start()
    
    def _scan_worker(self, directory: str):
        """Worker thread for directory scanning."""
        try:
            # Create progress window
            self.message_queue.put(("create_progress", "Scanning Directory"))
            
            # Progress callback
            def progress_callback(current, total, current_file):
                self.message_queue.put(("update_progress", (current, total, current_file, "Scanning files...")))
            
            # Scan directory
            self.log_message(f"Starting scan of: {directory}")
            result = self.scanner.scan_directory(directory, progress_callback)
            
            # Process found audio files
            if result.audio_files:
                self.message_queue.put(("update_progress", (0, len(result.audio_files), "", "Extracting metadata...")))
                
                processed = 0
                for audio_file in result.audio_files:
                    if hasattr(self.current_operation, 'cancelled') and self.current_operation.cancelled:
                        break
                    
                    # Extract metadata
                    metadata = self.extractor.extract_metadata(audio_file)
                    if metadata:
                        # Store in database
                        self.database.insert_song(metadata)
                    
                    processed += 1
                    self.message_queue.put(("update_progress", (processed, len(result.audio_files), str(audio_file), "Processing metadata...")))
            
            # Complete
            self.message_queue.put(("scan_complete", result))
            
        except Exception as e:
            self.message_queue.put(("scan_error", str(e)))
    
    def _process_messages(self):
        """Process messages from worker threads."""
        try:
            while True:
                message_type, data = self.message_queue.get_nowait()
                
                if message_type == "create_progress":
                    self.current_operation = ProgressWindow(self.root, data)
                
                elif message_type == "update_progress":
                    if self.current_operation:
                        current, total, current_file, operation = data
                        self.current_operation.update_progress(current, total, current_file, operation)
                
                elif message_type == "scan_complete":
                    result = data
                    if self.current_operation:
                        self.current_operation.set_completed(f"Scan complete: {len(result.audio_files)} audio files found")
                    
                    self.log_message(f"Scan completed: {len(result.audio_files)} audio files, {len(result.errors)} errors")
                    self.update_statistics()
                    self.scan_button.config(state="normal")
                
                elif message_type == "scan_error":
                    error_msg = data
                    if self.current_operation:
                        self.current_operation.set_completed(f"Scan failed: {error_msg}")
                    
                    self.log_message(f"Scan error: {error_msg}")
                    messagebox.showerror("Scan Error", f"Scan failed: {error_msg}")
                    self.scan_button.config(state="normal")
                
        except queue.Empty:
            pass
        
        # Schedule next check
        self.root.after(GUI_CONFIG['update_interval'], self._process_messages)
    
    def update_statistics(self):
        """Update the statistics display."""
        try:
            # Get database statistics
            db_stats = self.database.get_database_stats()
            scanner_stats = self.scanner.get_stats()
            extractor_stats = self.extractor.get_stats()
            
            stats_text = f"""Database Statistics:
Total Songs: {db_stats.get('total_songs', 0)}
Total Patterns: {db_stats.get('total_patterns', 0)}
Total Log Entries: {db_stats.get('total_log_entries', 0)}
Database Size: {db_stats.get('database_size', 0):,} bytes

Scanner Statistics:
Total Scans: {scanner_stats.get('total_scans', 0)}
Files Found: {scanner_stats.get('total_files_found', 0)}
Scan Errors: {scanner_stats.get('total_errors', 0)}

Extractor Statistics:
Total Extractions: {extractor_stats.get('total_extractions', 0)}
Successful: {extractor_stats.get('successful_extractions', 0)}
Failed: {extractor_stats.get('failed_extractions', 0)}
"""
            
            self.stats_text.delete(1.0, tk.END)
            self.stats_text.insert(1.0, stats_text)
            
        except Exception as e:
            self.logger.error(f"Error updating statistics: {e}")
    
    def log_message(self, message: str):
        """Add a message to the activity log."""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        
        # Keep log size manageable
        lines = self.log_text.get(1.0, tk.END).split('\n')
        if len(lines) > 1000:
            # Remove oldest lines
            self.log_text.delete(1.0, f"{len(lines) - 500}.0")
    
    def clear_cache(self):
        """Clear scanner cache."""
        try:
            self.scanner.clear_cache()
            self.log_message("Cache cleared")
            messagebox.showinfo("Success", "Cache cleared successfully")
        except Exception as e:
            self.log_message(f"Error clearing cache: {e}")
            messagebox.showerror("Error", f"Failed to clear cache: {e}")
    
    def backup_database(self):
        """Create database backup."""
        try:
            if self.database.backup_database():
                self.log_message("Database backup created")
                messagebox.showinfo("Success", "Database backup created successfully")
            else:
                self.log_message("Database backup failed")
                messagebox.showerror("Error", "Failed to create database backup")
        except Exception as e:
            self.log_message(f"Error creating backup: {e}")
            messagebox.showerror("Error", f"Failed to create backup: {e}")
    
    def show_about(self):
        """Show about dialog."""
        about_text = """AI Music Organization Assistant v0.1.0

A personalized AI that learns your music organization style 
and automatically applies it to new files.

This application implements the pipeline:
Scanner → Metadata → Patterns → Organization

Built with Python, tkinter, mutagen, librosa, and scikit-learn.
"""
        messagebox.showinfo("About", about_text)
    
    def run(self):
        """Start the GUI application."""
        try:
            self.root.mainloop()
        finally:
            # Clean up
            self.database.close_connections()


def main():
    """Main entry point for the GUI application."""
    app = MainWindow()
    app.run()


if __name__ == "__main__":
    main()
