"""
Pattern Recognition for AI Music Organization Assistant

This module implements the PatternRecognizer class that analyzes folder structures,
naming conventions, and organizational patterns in existing music collections.
This helps the AI learn the user's personal organizational DNA.

Author: AI Music Organization Assistant Project
"""

import logging
import re
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Set, Any
from dataclasses import dataclass, asdict
from collections import Counter, defaultdict
import json
from datetime import datetime

from .config import PATTERN_CONFIG


@dataclass
class FolderPattern:
    """Represents a detected folder structure pattern."""
    pattern_type: str  # 'hierarchy', 'naming', 'grouping'
    pattern_name: str
    pattern_description: str
    frequency: int
    confidence: float
    examples: List[str]
    depth_level: int
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class NamingPattern:
    """Represents a detected naming convention pattern."""
    pattern_type: str  # 'case_style', 'separator', 'format'
    pattern_name: str
    pattern_regex: str
    frequency: int
    confidence: float
    examples: List[str]
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class OrganizationalPattern:
    """Represents a high-level organizational pattern."""
    pattern_id: str
    pattern_type: str  # 'by_genre', 'by_artist', 'by_year', 'by_energy', etc.
    pattern_description: str
    folder_patterns: List[FolderPattern]
    naming_patterns: List[NamingPattern]
    metadata_patterns: Dict[str, Any]
    confidence_score: float
    usage_frequency: int
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


class PatternRecognizer:
    """
    Analyzes existing music collections to detect organizational patterns.
    
    This class examines folder structures, file naming conventions, and
    metadata usage patterns to understand how the user organizes their music.
    It's a key component in learning the user's "organizational DNA".
    
    Features:
    - Folder hierarchy analysis
    - Naming convention detection
    - Metadata usage pattern analysis
    - Confidence scoring for patterns
    - Pattern frequency tracking
    - Support for various organizational styles
    
    Usage:
        recognizer = PatternRecognizer()
        patterns = recognizer.analyze_collection("/path/to/music")
        for pattern in patterns:
            print(f"Pattern: {pattern.pattern_description}")
            print(f"Confidence: {pattern.confidence_score}")
    """
    
    def __init__(self, config: Optional[Dict] = None):
        """
        Initialize the PatternRecognizer.
        
        Args:
            config: Optional configuration dictionary
        """
        self.config = config or PATTERN_CONFIG
        self.logger = self._setup_logging()
        
        # Pattern detection rules
        self.case_patterns = {
            'camelCase': re.compile(r'^[a-z]+([A-Z][a-z]*)*$'),
            'PascalCase': re.compile(r'^[A-Z][a-z]*([A-Z][a-z]*)*$'),
            'snake_case': re.compile(r'^[a-z]+(_[a-z]+)*$'),
            'kebab-case': re.compile(r'^[a-z]+(-[a-z]+)*$'),
            'UPPER_CASE': re.compile(r'^[A-Z]+(_[A-Z]+)*$'),
            'Title Case': re.compile(r'^[A-Z][a-z]*(\s[A-Z][a-z]*)*$'),
            'lowercase': re.compile(r'^[a-z\s]+$'),
            'UPPERCASE': re.compile(r'^[A-Z\s]+$')
        }
        
        # Common separators
        self.separators = [' ', '_', '-', '.', '(', ')', '[', ']', '{', '}']
        
        # Statistics tracking
        self.stats = {
            'collections_analyzed': 0,
            'patterns_detected': 0,
            'folder_patterns': 0,
            'naming_patterns': 0,
            'last_analysis_time': None
        }
    
    def _setup_logging(self) -> logging.Logger:
        """Set up logging for the pattern recognizer."""
        logger = logging.getLogger(__name__)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def analyze_collection(self, root_path: str | Path, 
                          song_metadata: Optional[List[Dict]] = None) -> List[OrganizationalPattern]:
        """
        Analyze a music collection to detect organizational patterns.
        
        Args:
            root_path: Root directory of the music collection
            song_metadata: Optional list of song metadata dictionaries
            
        Returns:
            List of detected organizational patterns
        """
        root_path = Path(root_path)
        
        if not root_path.exists():
            self.logger.error(f"Collection path not found: {root_path}")
            return []
        
        self.logger.info(f"Analyzing collection patterns: {root_path}")
        start_time = datetime.now()
        
        try:
            self.stats['collections_analyzed'] += 1
            
            # Collect folder structure information
            folder_structure = self._analyze_folder_structure(root_path)
            
            # Analyze naming conventions
            naming_patterns = self._analyze_naming_conventions(root_path)
            
            # Analyze folder hierarchy patterns
            hierarchy_patterns = self._analyze_hierarchy_patterns(folder_structure)
            
            # Analyze metadata patterns if provided
            metadata_patterns = {}
            if song_metadata:
                metadata_patterns = self._analyze_metadata_patterns(song_metadata)
            
            # Combine patterns into organizational patterns
            organizational_patterns = self._combine_patterns(
                hierarchy_patterns, naming_patterns, metadata_patterns
            )
            
            # Update statistics
            self.stats['patterns_detected'] += len(organizational_patterns)
            self.stats['folder_patterns'] += len(hierarchy_patterns)
            self.stats['naming_patterns'] += len(naming_patterns)
            self.stats['last_analysis_time'] = datetime.now().isoformat()
            
            analysis_time = (datetime.now() - start_time).total_seconds()
            self.logger.info(
                f"Pattern analysis completed in {analysis_time:.2f}s: "
                f"{len(organizational_patterns)} patterns detected"
            )
            
            return organizational_patterns
            
        except Exception as e:
            self.logger.error(f"Pattern analysis failed: {e}")
            return []
    
    def _analyze_folder_structure(self, root_path: Path) -> Dict[str, Any]:
        """Analyze the folder structure of the collection."""
        structure = {
            'total_folders': 0,
            'max_depth': 0,
            'folders_by_depth': defaultdict(list),
            'folder_names': [],
            'folder_paths': []
        }
        
        try:
            for folder_path in root_path.rglob('*'):
                if folder_path.is_dir():
                    structure['total_folders'] += 1
                    
                    # Calculate depth
                    try:
                        relative_path = folder_path.relative_to(root_path)
                        depth = len(relative_path.parts)
                        structure['max_depth'] = max(structure['max_depth'], depth)
                        structure['folders_by_depth'][depth].append(str(relative_path))
                        structure['folder_names'].append(folder_path.name)
                        structure['folder_paths'].append(str(relative_path))
                    except ValueError:
                        # Skip if path is not relative to root
                        continue
            
            self.logger.debug(f"Folder structure: {structure['total_folders']} folders, max depth {structure['max_depth']}")
            
        except Exception as e:
            self.logger.error(f"Error analyzing folder structure: {e}")
        
        return structure
    
    def _analyze_naming_conventions(self, root_path: Path) -> List[NamingPattern]:
        """Analyze naming conventions used in folder and file names."""
        naming_patterns = []
        all_names = []
        
        try:
            # Collect all folder and file names
            for item_path in root_path.rglob('*'):
                if item_path.is_dir() or item_path.suffix.lower() in {'.mp3', '.flac', '.wav', '.m4a', '.ogg', '.aac', '.wma'}:
                    name = item_path.stem if item_path.is_file() else item_path.name
                    all_names.append(name)
            
            if not all_names:
                return naming_patterns
            
            # Analyze case patterns
            case_counts = Counter()
            for name in all_names:
                for case_name, pattern in self.case_patterns.items():
                    if pattern.match(name):
                        case_counts[case_name] += 1
                        break
            
            # Create naming patterns for significant case styles
            total_names = len(all_names)
            for case_style, count in case_counts.most_common():
                if count >= self.config['min_pattern_frequency']:
                    confidence = count / total_names
                    if confidence >= 0.1:  # At least 10% usage
                        examples = [name for name in all_names[:10] if self.case_patterns[case_style].match(name)]
                        
                        pattern = NamingPattern(
                            pattern_type='case_style',
                            pattern_name=case_style,
                            pattern_regex=self.case_patterns[case_style].pattern,
                            frequency=count,
                            confidence=confidence,
                            examples=examples[:5]
                        )
                        naming_patterns.append(pattern)
            
            # Analyze separator usage
            separator_counts = Counter()
            for name in all_names:
                for sep in self.separators:
                    if sep in name:
                        separator_counts[sep] += 1
            
            # Create patterns for common separators
            for separator, count in separator_counts.most_common():
                if count >= self.config['min_pattern_frequency']:
                    confidence = count / total_names
                    if confidence >= 0.1:
                        examples = [name for name in all_names[:10] if separator in name]
                        
                        pattern = NamingPattern(
                            pattern_type='separator',
                            pattern_name=f"Uses '{separator}' separator",
                            pattern_regex=re.escape(separator),
                            frequency=count,
                            confidence=confidence,
                            examples=examples[:5]
                        )
                        naming_patterns.append(pattern)
            
            self.logger.debug(f"Detected {len(naming_patterns)} naming patterns")
            
        except Exception as e:
            self.logger.error(f"Error analyzing naming conventions: {e}")
        
        return naming_patterns
    
    def _analyze_hierarchy_patterns(self, folder_structure: Dict[str, Any]) -> List[FolderPattern]:
        """Analyze folder hierarchy patterns."""
        hierarchy_patterns = []
        
        try:
            # Analyze patterns by depth level
            for depth, folders in folder_structure['folders_by_depth'].items():
                if depth == 0 or len(folders) < self.config['min_pattern_frequency']:
                    continue
                
                # Extract folder names at this depth
                folder_names = []
                for folder_path in folders:
                    parts = folder_path.split('/')
                    if len(parts) >= depth:
                        folder_names.append(parts[depth - 1])
                
                # Look for common patterns
                name_counts = Counter(folder_names)
                
                # Check for genre-based organization
                genre_keywords = ['rock', 'pop', 'jazz', 'classical', 'electronic', 'hip-hop', 'country', 'blues']
                genre_matches = sum(1 for name in folder_names if any(genre in name.lower() for genre in genre_keywords))
                
                if genre_matches >= self.config['min_pattern_frequency']:
                    confidence = genre_matches / len(folder_names)
                    pattern = FolderPattern(
                        pattern_type='grouping',
                        pattern_name='genre_organization',
                        pattern_description=f'Organizes music by genre at depth {depth}',
                        frequency=genre_matches,
                        confidence=confidence,
                        examples=[name for name in folder_names if any(genre in name.lower() for genre in genre_keywords)][:5],
                        depth_level=depth
                    )
                    hierarchy_patterns.append(pattern)
                
                # Check for artist-based organization
                # Look for patterns that might be artist names (proper case, multiple words)
                artist_pattern = re.compile(r'^[A-Z][a-z]+(\s[A-Z][a-z]+)*$')
                artist_matches = sum(1 for name in folder_names if artist_pattern.match(name))
                
                if artist_matches >= self.config['min_pattern_frequency']:
                    confidence = artist_matches / len(folder_names)
                    pattern = FolderPattern(
                        pattern_type='grouping',
                        pattern_name='artist_organization',
                        pattern_description=f'Organizes music by artist at depth {depth}',
                        frequency=artist_matches,
                        confidence=confidence,
                        examples=[name for name in folder_names if artist_pattern.match(name)][:5],
                        depth_level=depth
                    )
                    hierarchy_patterns.append(pattern)
                
                # Check for year-based organization
                year_pattern = re.compile(r'(19|20)\d{2}')
                year_matches = sum(1 for name in folder_names if year_pattern.search(name))
                
                if year_matches >= self.config['min_pattern_frequency']:
                    confidence = year_matches / len(folder_names)
                    pattern = FolderPattern(
                        pattern_type='grouping',
                        pattern_name='year_organization',
                        pattern_description=f'Organizes music by year at depth {depth}',
                        frequency=year_matches,
                        confidence=confidence,
                        examples=[name for name in folder_names if year_pattern.search(name)][:5],
                        depth_level=depth
                    )
                    hierarchy_patterns.append(pattern)
            
            self.logger.debug(f"Detected {len(hierarchy_patterns)} hierarchy patterns")
            
        except Exception as e:
            self.logger.error(f"Error analyzing hierarchy patterns: {e}")
        
        return hierarchy_patterns
    
    def _analyze_metadata_patterns(self, song_metadata: List[Dict]) -> Dict[str, Any]:
        """Analyze metadata usage patterns."""
        patterns = {
            'field_usage': {},
            'field_formats': {},
            'vocabulary_patterns': {}
        }
        
        try:
            if not song_metadata:
                return patterns
            
            # Analyze field usage frequency
            field_counts = Counter()
            total_songs = len(song_metadata)
            
            for song in song_metadata:
                for field, value in song.items():
                    if value is not None and str(value).strip():
                        field_counts[field] += 1
            
            # Calculate usage percentages
            for field, count in field_counts.items():
                patterns['field_usage'][field] = {
                    'count': count,
                    'percentage': count / total_songs,
                    'consistency': 'high' if count / total_songs > 0.8 else 'medium' if count / total_songs > 0.5 else 'low'
                }
            
            # Analyze genre vocabulary
            if 'genre' in field_counts:
                genres = [song.get('genre', '').strip() for song in song_metadata if song.get('genre')]
                genre_counts = Counter(genres)
                patterns['vocabulary_patterns']['genres'] = dict(genre_counts.most_common(20))
            
            # Analyze comment patterns
            if 'comment' in field_counts:
                comments = [song.get('comment', '').strip() for song in song_metadata if song.get('comment')]
                if comments:
                    # Extract common words from comments
                    all_words = []
                    for comment in comments:
                        words = re.findall(r'\b\w+\b', comment.lower())
                        all_words.extend(words)
                    
                    word_counts = Counter(all_words)
                    patterns['vocabulary_patterns']['comment_words'] = dict(word_counts.most_common(50))
            
            self.logger.debug(f"Analyzed metadata patterns for {total_songs} songs")
            
        except Exception as e:
            self.logger.error(f"Error analyzing metadata patterns: {e}")
        
        return patterns
    
    def _combine_patterns(self, hierarchy_patterns: List[FolderPattern], 
                         naming_patterns: List[NamingPattern],
                         metadata_patterns: Dict[str, Any]) -> List[OrganizationalPattern]:
        """Combine individual patterns into organizational patterns."""
        organizational_patterns = []
        
        try:
            # Group patterns by type
            pattern_groups = defaultdict(list)
            
            for pattern in hierarchy_patterns:
                pattern_groups[pattern.pattern_name].append(pattern)
            
            # Create organizational patterns
            for pattern_type, patterns in pattern_groups.items():
                if not patterns:
                    continue
                
                # Calculate overall confidence
                total_frequency = sum(p.frequency for p in patterns)
                avg_confidence = sum(p.confidence for p in patterns) / len(patterns)
                
                # Create organizational pattern
                org_pattern = OrganizationalPattern(
                    pattern_id=f"{pattern_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    pattern_type=pattern_type,
                    pattern_description=patterns[0].pattern_description,
                    folder_patterns=patterns,
                    naming_patterns=[p for p in naming_patterns if p.confidence > 0.2],
                    metadata_patterns=metadata_patterns,
                    confidence_score=avg_confidence,
                    usage_frequency=total_frequency
                )
                
                organizational_patterns.append(org_pattern)
            
            # Sort by confidence score
            organizational_patterns.sort(key=lambda p: p.confidence_score, reverse=True)
            
        except Exception as e:
            self.logger.error(f"Error combining patterns: {e}")
        
        return organizational_patterns
    
    def get_stats(self) -> Dict[str, Any]:
        """Get pattern recognition statistics."""
        return self.stats.copy()
    
    def save_patterns(self, patterns: List[OrganizationalPattern], output_path: str | Path) -> bool:
        """Save detected patterns to a JSON file."""
        try:
            output_path = Path(output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            patterns_data = [pattern.to_dict() for pattern in patterns]
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(patterns_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Saved {len(patterns)} patterns to {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error saving patterns: {e}")
            return False


# Example usage and testing
if __name__ == "__main__":
    # Example usage
    recognizer = PatternRecognizer()
    
    # This would analyze a collection - replace with actual path for testing
    # patterns = recognizer.analyze_collection("/path/to/music/collection")
    # for pattern in patterns:
    #     print(f"Pattern: {pattern.pattern_description}")
    #     print(f"Confidence: {pattern.confidence_score:.2f}")
    #     print(f"Usage: {pattern.usage_frequency}")
    
    print("PatternRecognizer initialized successfully")
