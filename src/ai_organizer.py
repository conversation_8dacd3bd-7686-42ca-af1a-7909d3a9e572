"""
AI Music Organizer - Complete Integration Module

This module integrates all components of the AI Music Organization Assistant
to provide a complete, intelligent music organization system. This is the
main orchestrator that combines scanning, analysis, pattern learning, and
organization prediction.

Author: AI Music Organization Assistant Project
"""

import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any, Callable
from dataclasses import dataclass
import json
from datetime import datetime

from .scanner import MusicScanner, ScanResult
from .metadata import MetadataExtractor, AudioMetadata
from .database import DatabaseManager
from .audio_analysis import AudioAnalyzer, AudioFeatures
from .pattern_recognition import <PERSON><PERSON><PERSON>ecog<PERSON><PERSON>, OrganizationalPattern
from .nlp_analysis import CommentAnalyzer, CommentAnalysis
from .pattern_learning import <PERSON><PERSON><PERSON><PERSON><PERSON>, PredictionResult
from .config import get_config


@dataclass
class OrganizationSuggestion:
    """Complete organization suggestion for a music file."""
    file_path: str
    suggested_path: str
    confidence_score: float
    reasoning: List[str]
    alternative_suggestions: List[Tuple[str, float]]
    metadata: AudioMetadata
    audio_features: Optional[AudioFeatures]
    prediction_result: PredictionResult
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'file_path': self.file_path,
            'suggested_path': self.suggested_path,
            'confidence_score': self.confidence_score,
            'reasoning': self.reasoning,
            'alternative_suggestions': self.alternative_suggestions,
            'metadata': self.metadata.__dict__ if self.metadata else None,
            'audio_features': self.audio_features.__dict__ if self.audio_features else None,
            'prediction_result': self.prediction_result.to_dict() if self.prediction_result else None
        }


@dataclass
class AnalysisReport:
    """Complete analysis report for a music collection."""
    collection_path: str
    total_files: int
    analyzed_files: int
    organizational_patterns: List[OrganizationalPattern]
    comment_analysis: Optional[CommentAnalysis]
    suggestions: List[OrganizationSuggestion]
    analysis_time: float
    confidence_distribution: Dict[str, int]
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'collection_path': self.collection_path,
            'total_files': self.total_files,
            'analyzed_files': self.analyzed_files,
            'organizational_patterns': [p.to_dict() for p in self.organizational_patterns],
            'comment_analysis': self.comment_analysis.to_dict() if self.comment_analysis else None,
            'suggestions': [s.to_dict() for s in self.suggestions],
            'analysis_time': self.analysis_time,
            'confidence_distribution': self.confidence_distribution
        }


class AIOrganizer:
    """
    Complete AI Music Organization System.
    
    This class orchestrates all components to provide intelligent music
    organization capabilities. It learns from existing collections and
    provides suggestions for organizing new music files.
    
    Features:
    - Complete collection analysis
    - Pattern learning from existing organization
    - Intelligent suggestions for new files
    - Confidence scoring and explanations
    - Incremental learning capabilities
    - Progress tracking for long operations
    
    Usage:
        organizer = AIOrganizer()
        
        # Analyze existing collection to learn patterns
        report = organizer.analyze_collection("/path/to/music")
        
        # Get suggestions for organizing new files
        suggestions = organizer.get_organization_suggestions(new_files)
        
        # Apply suggestions with user confirmation
        organizer.apply_suggestions(suggestions, confirm_callback)
    """
    
    def __init__(self, database_path: Optional[Path] = None):
        """
        Initialize the AI Organizer.
        
        Args:
            database_path: Optional path to database file
        """
        self.config = get_config()
        self.logger = self._setup_logging()
        
        # Initialize all components
        self.scanner = MusicScanner()
        self.metadata_extractor = MetadataExtractor()
        self.database = DatabaseManager(database_path)
        self.audio_analyzer = AudioAnalyzer()
        self.pattern_recognizer = PatternRecognizer()
        self.comment_analyzer = CommentAnalyzer()
        self.pattern_learner = PatternLearner()
        
        # Initialize database
        if not self.database.initialize_database():
            raise RuntimeError("Failed to initialize database")
        
        # State tracking
        self.is_trained = False
        self.last_analysis_time = None
        self.learned_patterns = []
        
        # Statistics
        self.stats = {
            'collections_analyzed': 0,
            'files_analyzed': 0,
            'suggestions_generated': 0,
            'patterns_learned': 0,
            'average_confidence': 0.0
        }
    
    def _setup_logging(self) -> logging.Logger:
        """Set up logging for the AI organizer."""
        logger = logging.getLogger(__name__)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def analyze_collection(self, 
                          collection_path: str | Path,
                          progress_callback: Optional[Callable[[str, int, int], None]] = None,
                          analyze_audio: bool = True,
                          train_models: bool = True) -> AnalysisReport:
        """
        Perform complete analysis of a music collection.
        
        Args:
            collection_path: Path to the music collection
            progress_callback: Optional progress callback function
            analyze_audio: Whether to perform audio analysis
            train_models: Whether to train ML models after analysis
            
        Returns:
            AnalysisReport with complete analysis results
        """
        collection_path = Path(collection_path)
        self.logger.info(f"Starting complete collection analysis: {collection_path}")
        start_time = datetime.now()
        
        try:
            # Phase 1: Scan for music files
            if progress_callback:
                progress_callback("Scanning for music files...", 0, 100)
            
            scan_result = self.scanner.scan_directory(
                collection_path,
                lambda current, total, file: progress_callback(f"Scanning: {Path(file).name}", current * 20 // total, 100) if progress_callback else None
            )
            
            if not scan_result.audio_files:
                self.logger.warning("No audio files found in collection")
                return self._create_empty_report(collection_path, start_time)
            
            # Phase 2: Extract metadata
            if progress_callback:
                progress_callback("Extracting metadata...", 20, 100)
            
            songs_metadata = []
            audio_features_list = []
            
            for i, audio_file in enumerate(scan_result.audio_files):
                if progress_callback:
                    progress = 20 + (i * 30 // len(scan_result.audio_files))
                    progress_callback(f"Processing: {audio_file.name}", progress, 100)
                
                # Extract metadata
                metadata = self.metadata_extractor.extract_metadata(audio_file)
                if metadata:
                    songs_metadata.append(self.metadata_extractor.to_dict(metadata))
                    
                    # Store in database
                    self.database.insert_song(metadata)
                    
                    # Extract audio features if requested
                    if analyze_audio:
                        audio_features = self.audio_analyzer.analyze_audio(audio_file)
                        audio_features_list.append(audio_features)
                    else:
                        audio_features_list.append(None)
            
            # Phase 3: Analyze patterns
            if progress_callback:
                progress_callback("Analyzing organizational patterns...", 50, 100)
            
            organizational_patterns = self.pattern_recognizer.analyze_collection(
                collection_path, songs_metadata
            )
            
            # Phase 4: Analyze comments
            if progress_callback:
                progress_callback("Analyzing comment patterns...", 70, 100)
            
            comments = [song.get('comment', '') for song in songs_metadata if song.get('comment')]
            comment_analysis = None
            if comments:
                comment_analysis = self.comment_analyzer.analyze_comments(comments)
            
            # Phase 5: Train ML models
            if train_models and songs_metadata:
                if progress_callback:
                    progress_callback("Training AI models...", 80, 100)
                
                try:
                    # Prepare training data
                    X, y = self.pattern_learner.prepare_training_data(
                        songs_metadata, audio_features_list, organizational_patterns, comment_analysis
                    )
                    
                    # Train models
                    performance = self.pattern_learner.train_models(X, y)
                    self.is_trained = True
                    
                    self.logger.info(f"Model training completed - Accuracy: {performance.accuracy:.3f}")
                    
                except Exception as e:
                    self.logger.warning(f"Model training failed: {e}")
            
            # Phase 6: Generate suggestions for existing files
            suggestions = []
            if self.is_trained:
                if progress_callback:
                    progress_callback("Generating organization suggestions...", 90, 100)
                
                suggestions = self._generate_suggestions_for_collection(
                    songs_metadata, audio_features_list, comment_analysis
                )
            
            # Calculate confidence distribution
            confidence_distribution = self._calculate_confidence_distribution(suggestions)
            
            # Create analysis report
            analysis_time = (datetime.now() - start_time).total_seconds()
            
            report = AnalysisReport(
                collection_path=str(collection_path),
                total_files=len(scan_result.audio_files),
                analyzed_files=len(songs_metadata),
                organizational_patterns=organizational_patterns,
                comment_analysis=comment_analysis,
                suggestions=suggestions,
                analysis_time=analysis_time,
                confidence_distribution=confidence_distribution
            )
            
            # Update statistics
            self.stats['collections_analyzed'] += 1
            self.stats['files_analyzed'] += len(songs_metadata)
            self.stats['patterns_learned'] += len(organizational_patterns)
            self.last_analysis_time = datetime.now().isoformat()
            self.learned_patterns = organizational_patterns
            
            if progress_callback:
                progress_callback("Analysis complete!", 100, 100)
            
            self.logger.info(
                f"Collection analysis completed in {analysis_time:.2f}s: "
                f"{len(songs_metadata)} files, {len(organizational_patterns)} patterns, "
                f"{len(suggestions)} suggestions"
            )
            
            return report
            
        except Exception as e:
            self.logger.error(f"Collection analysis failed: {e}")
            raise
    
    def get_organization_suggestions(self, 
                                   file_paths: List[str | Path],
                                   progress_callback: Optional[Callable[[str, int, int], None]] = None) -> List[OrganizationSuggestion]:
        """
        Get organization suggestions for new music files.
        
        Args:
            file_paths: List of paths to music files
            progress_callback: Optional progress callback
            
        Returns:
            List of organization suggestions
        """
        if not self.is_trained:
            raise ValueError("AI models must be trained before generating suggestions")
        
        suggestions = []
        
        try:
            for i, file_path in enumerate(file_paths):
                if progress_callback:
                    progress = (i * 100) // len(file_paths)
                    progress_callback(f"Analyzing: {Path(file_path).name}", progress, 100)
                
                suggestion = self._get_single_file_suggestion(file_path)
                if suggestion:
                    suggestions.append(suggestion)
            
            self.stats['suggestions_generated'] += len(suggestions)
            
            if progress_callback:
                progress_callback("Suggestions complete!", 100, 100)
            
            return suggestions
            
        except Exception as e:
            self.logger.error(f"Error generating suggestions: {e}")
            return []
    
    def _get_single_file_suggestion(self, file_path: str | Path) -> Optional[OrganizationSuggestion]:
        """Get organization suggestion for a single file."""
        try:
            file_path = Path(file_path)
            
            # Extract metadata
            metadata = self.metadata_extractor.extract_metadata(file_path)
            if not metadata:
                return None
            
            # Extract audio features
            audio_features = self.audio_analyzer.analyze_audio(file_path)
            
            # Get prediction from ML model
            prediction = self.pattern_learner.predict_organization(
                self.metadata_extractor.to_dict(metadata),
                audio_features,
                None  # Comment analysis would need to be done per-file
            )
            
            # Create suggestion
            suggestion = OrganizationSuggestion(
                file_path=str(file_path),
                suggested_path=prediction.predicted_path,
                confidence_score=prediction.confidence_score,
                reasoning=prediction.reasoning,
                alternative_suggestions=prediction.alternative_suggestions,
                metadata=metadata,
                audio_features=audio_features,
                prediction_result=prediction
            )
            
            return suggestion
            
        except Exception as e:
            self.logger.error(f"Error getting suggestion for {file_path}: {e}")
            return None
    
    def _generate_suggestions_for_collection(self, 
                                           songs_metadata: List[Dict],
                                           audio_features_list: List[Optional[AudioFeatures]],
                                           comment_analysis: Optional[CommentAnalysis]) -> List[OrganizationSuggestion]:
        """Generate suggestions for all files in the analyzed collection."""
        suggestions = []
        
        for i, song_metadata in enumerate(songs_metadata):
            try:
                audio_features = audio_features_list[i] if i < len(audio_features_list) else None
                
                # Get prediction
                prediction = self.pattern_learner.predict_organization(
                    song_metadata, audio_features, comment_analysis
                )
                
                # Create metadata object
                metadata = AudioMetadata(
                    file_path=song_metadata['file_path'],
                    file_format=song_metadata['file_format'],
                    file_size=song_metadata['file_size'],
                    duration=song_metadata['duration'],
                    bitrate=song_metadata['bitrate'],
                    sample_rate=song_metadata['sample_rate'],
                    title=song_metadata.get('title'),
                    artist=song_metadata.get('artist'),
                    album=song_metadata.get('album'),
                    genre=song_metadata.get('genre'),
                    year=song_metadata.get('year')
                )
                
                suggestion = OrganizationSuggestion(
                    file_path=song_metadata['file_path'],
                    suggested_path=prediction.predicted_path,
                    confidence_score=prediction.confidence_score,
                    reasoning=prediction.reasoning,
                    alternative_suggestions=prediction.alternative_suggestions,
                    metadata=metadata,
                    audio_features=audio_features,
                    prediction_result=prediction
                )
                
                suggestions.append(suggestion)
                
            except Exception as e:
                self.logger.warning(f"Error generating suggestion for {song_metadata.get('file_path', 'unknown')}: {e}")
                continue
        
        return suggestions
    
    def _calculate_confidence_distribution(self, suggestions: List[OrganizationSuggestion]) -> Dict[str, int]:
        """Calculate distribution of confidence scores."""
        distribution = {'high': 0, 'medium': 0, 'low': 0}
        
        for suggestion in suggestions:
            if suggestion.confidence_score >= 0.8:
                distribution['high'] += 1
            elif suggestion.confidence_score >= 0.5:
                distribution['medium'] += 1
            else:
                distribution['low'] += 1
        
        return distribution
    
    def _create_empty_report(self, collection_path: Path, start_time: datetime) -> AnalysisReport:
        """Create an empty analysis report."""
        analysis_time = (datetime.now() - start_time).total_seconds()
        
        return AnalysisReport(
            collection_path=str(collection_path),
            total_files=0,
            analyzed_files=0,
            organizational_patterns=[],
            comment_analysis=None,
            suggestions=[],
            analysis_time=analysis_time,
            confidence_distribution={'high': 0, 'medium': 0, 'low': 0}
        )
    
    def save_analysis_report(self, report: AnalysisReport, output_path: str | Path) -> bool:
        """Save analysis report to JSON file."""
        try:
            output_path = Path(output_path)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(report.to_dict(), f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Analysis report saved to {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error saving analysis report: {e}")
            return False
    
    def save_models(self, model_path: str | Path) -> bool:
        """Save trained models to disk."""
        return self.pattern_learner.save_models(model_path)
    
    def load_models(self, model_path: str | Path) -> bool:
        """Load trained models from disk."""
        success = self.pattern_learner.load_models(model_path)
        if success:
            self.is_trained = True
        return success
    
    def get_stats(self) -> Dict[str, Any]:
        """Get comprehensive statistics from all components."""
        stats = self.stats.copy()
        
        # Add component statistics
        stats['scanner'] = self.scanner.get_stats()
        stats['metadata_extractor'] = self.metadata_extractor.get_stats()
        stats['database'] = self.database.get_database_stats()
        stats['audio_analyzer'] = self.audio_analyzer.get_stats()
        stats['pattern_recognizer'] = self.pattern_recognizer.get_stats()
        stats['comment_analyzer'] = self.comment_analyzer.get_stats()
        stats['pattern_learner'] = self.pattern_learner.get_stats()
        
        return stats
    
    def close(self):
        """Clean up resources."""
        self.database.close_connections()


# Example usage and testing
if __name__ == "__main__":
    # Example usage
    organizer = AIOrganizer()
    
    print("AI Music Organizer initialized successfully")
    print(f"Configuration: {organizer.config['project']}")
    print(f"Statistics: {organizer.get_stats()}")
