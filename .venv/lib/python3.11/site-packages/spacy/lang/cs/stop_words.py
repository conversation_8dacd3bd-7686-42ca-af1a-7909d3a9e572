# Source: https://github.com/Alir3z4/stop-words
# Source: https://github.com/stopwords-iso/stopwords-cs/blob/master/stopwords-cs.txt

STOP_WORDS = set(
    """
a
aby
ahoj
ačkoli
ale
alespoň
anebo
ani
aniž
ano
atd.
atp.
asi
aspoň
až
během
bez
beze
blízko
bohužel
brzo
bude
budeme
budeš
budete
budou
budu
by
byl
byla
byli
bylo
byly
bys
být
čau
chce
chceme
chceš
chcete
chci
chtějí
chtít
chuť
chuti
co
což
cz
či
článek
článku
články
čtrnáct
čtyři
dál
dále
daleko
dalš<PERSON>
děkovat
děkujeme
děkuji
den
deset
devatenáct
devět
dnes
do
dobrý
docela
dva
dvacet
dvanáct
dvě
email
ho
hodně
i
já
jak
jakmile
jako
jakož
jde
je
jeden
jedenáct
jedna
jedno
jednou
jedou
jeho
jehož
jej
její
jejich
jejichž
jehož
jeli<PERSON>ž
jemu
jen
jenom
jenž
jež
ještě
jestli
jestli<PERSON>e
ještě
ji
jí
jich
jím
jim
jimi
jinak
jiné
již
jsi
jsme
jsem
jsou
jste
k
kam
každý
kde
kdo
kdy
když
ke
kolik
kromě
která
kterak
kterou
které
kteří
který
kvůli
ku
má
mají
málo
mám
máme
máš
máte
mé
mě
mezi
mi
mí
mít
mne
mně
mnou
moc
mohl
mohou
moje
moji
možná
můj
musí
může
my
na
nad
nade
nám
námi
naproti
nás
náš
naše
naši
načež
ne
ně
nebo
nebyl
nebyla
nebyli
nebyly
nechť
něco
nedělá
nedělají
nedělám
neděláme
neděláš
neděláte
nějak
nejsi
nejsou
někde
někdo
nemají
nemáme
nemáte
neměl
němu
němuž
není
nestačí
ně
nevadí
nové
nový
noví
než
nic
nich
ní
ním
nimi
nula
o
od
ode
on
ona
oni
ono
ony
osm
osmnáct
pak
patnáct
pět
po
pod
pokud
pořád
pouze
potom
pozdě
pravé
před
přede
přes
přece
pro
proč
prosím
prostě
proto
proti
první
právě
protože
při
přičemž
rovně
s
se
sedm
sedmnáct
si
sice
skoro
sic
šest
šestnáct
skoro
smějí
smí
snad
spolu
sta
svůj
své
svá
svých
svým
svými
svůj
sté
sto
strana
ta
tady
tak
takhle
taky
také
takže
tam
támhle
támhleto
tamto
tě
tebe
tebou
teď
tedy
ten
tento
této
ti
tím
tímto
tisíc
tisíce
to
tobě
tohle
tohoto
tom
tomto
tomu
tomuto
toto
třeba
tři
třináct
trošku
trochu
tu
tuto
tvá
tvé
tvoje
tvůj
ty
tyto
těm
těma
těmi
u
určitě
už
v
vám
vámi
vás
váš
vaše
vaši
ve
večer
vedle
více
vlastně
však
všechen
všechno
všichni
vůbec
vy
vždy
z
zda
za
zde
zač
zatímco
ze
že
""".split()
)
