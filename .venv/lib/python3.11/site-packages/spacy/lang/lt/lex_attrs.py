from ...attrs import LIKE_NUM

_num_words = {
    "antra",
    "antrai",
    "antrais",
    "antram",
    "antrame",
    "antras",
    "antri",
    "antriems",
    "antro",
    "antroje",
    "antromis",
    "antroms",
    "antros",
    "antrose",
    "antru",
    "antruose",
    "antrus",
    "antrą",
    "antrų",
    "aštunta",
    "aštuntai",
    "aštuntais",
    "aštuntam",
    "aštuntame",
    "aštuntas",
    "aštunti",
    "aštuntiems",
    "aštunto",
    "aštunto<PERSON>",
    "aštuntomis",
    "aštuntoms",
    "aštun<PERSON>",
    "aštuntose",
    "aštuntu",
    "aštuntuose",
    "aštuntus",
    "aštuntą",
    "aštuntų",
    "aštuoneri",
    "aštuoneriais",
    "aštuonerias",
    "aštu<PERSON>riems",
    "aštu<PERSON>rio<PERSON>",
    "aštuonerio<PERSON>",
    "aštu<PERSON>rios",
    "aštu<PERSON><PERSON><PERSON>",
    "aštu<PERSON><PERSON><PERSON>se",
    "a<PERSON><PERSON><PERSON><PERSON>",
    "aš<PERSON><PERSON><PERSON><PERSON>",
    "aš<PERSON><PERSON><PERSON>",
    "aš<PERSON><PERSON>tai<PERSON>",
    "aštuonetams",
    "aštuonetas",
    "aštuonete",
    "aštuoneto",
    "aštuonetu",
    "aštuonetui",
    "aštuonetuose",
    "aštuonetus",
    "aštuonetą",
    "aštuonetų",
    "aštuoni",
    "aštuoniais",
    "aštuonias",
    "aštuoniasdešimt",
    "aštuoniasdešimta",
    "aštuoniasdešimtai",
    "aštuoniasdešimtais",
    "aštuoniasdešimtam",
    "aštuoniasdešimtame",
    "aštuoniasdešimtas",
    "aštuoniasdešimti",
    "aštuoniasdešimtiems",
    "aštuoniasdešimties",
    "aštuoniasdešimtimi",
    "aštuoniasdešimtimis",
    "aštuoniasdešimtims",
    "aštuoniasdešimtis",
    "aštuoniasdešimto",
    "aštuoniasdešimtoje",
    "aštuoniasdešimtomis",
    "aštuoniasdešimtoms",
    "aštuoniasdešimtos",
    "aštuoniasdešimtose",
    "aštuoniasdešimtu",
    "aštuoniasdešimtuose",
    "aštuoniasdešimtus",
    "aštuoniasdešimtyje",
    "aštuoniasdešimtys",
    "aštuoniasdešimtyse",
    "aštuoniasdešimtą",
    "aštuoniasdešimtį",
    "aštuoniasdešimtų",
    "aštuoniasdešimčia",
    "aštuoniasdešimčiai",
    "aštuoniasdešimčių",
    "aštuoniems",
    "aštuoniolika",
    "aštuoniolikai",
    "aštuoniolikoje",
    "aštuoniolikos",
    "aštuoniolikta",
    "aštuonioliktai",
    "aštuonioliktais",
    "aštuonioliktam",
    "aštuonioliktame",
    "aštuonioliktas",
    "aštuoniolikti",
    "aštuonioliktiems",
    "aštuoniolikto",
    "aštuonioliktoje",
    "aštuonioliktomis",
    "aštuonioliktoms",
    "aštuonioliktos",
    "aštuonioliktose",
    "aštuonioliktu",
    "aštuonioliktuose",
    "aštuonioliktus",
    "aštuonioliktą",
    "aštuonioliktų",
    "aštuoniomis",
    "aštuonioms",
    "aštuonios",
    "aštuoniose",
    "aštuonis",
    "aštuoniuose",
    "aštuonių",
    "bilijonai",
    "bilijonais",
    "bilijonams",
    "bilijonas",
    "bilijone",
    "bilijono",
    "bilijonu",
    "bilijonui",
    "bilijonuose",
    "bilijonus",
    "bilijoną",
    "bilijonų",
    "devinta",
    "devintai",
    "devintais",
    "devintam",
    "devintame",
    "devintas",
    "devinti",
    "devintiems",
    "devinto",
    "devintoje",
    "devintomis",
    "devintoms",
    "devintos",
    "devintose",
    "devintu",
    "devintuose",
    "devintus",
    "devintą",
    "devintų",
    "devyneri",
    "devyneriais",
    "devynerias",
    "devyneriems",
    "devyneriomis",
    "devynerioms",
    "devynerios",
    "devyneriose",
    "devyneriuose",
    "devynerius",
    "devynerių",
    "devynetas",
    "devynete",
    "devyneto",
    "devynetu",
    "devynetui",
    "devynetą",
    "devyni",
    "devyniais",
    "devynias",
    "devyniasdešimt",
    "devyniasdešimta",
    "devyniasdešimtai",
    "devyniasdešimtais",
    "devyniasdešimtam",
    "devyniasdešimtame",
    "devyniasdešimtas",
    "devyniasdešimti",
    "devyniasdešimtiems",
    "devyniasdešimties",
    "devyniasdešimtimi",
    "devyniasdešimtimis",
    "devyniasdešimtims",
    "devyniasdešimtis",
    "devyniasdešimto",
    "devyniasdešimtoje",
    "devyniasdešimtomis",
    "devyniasdešimtoms",
    "devyniasdešimtos",
    "devyniasdešimtose",
    "devyniasdešimtu",
    "devyniasdešimtuose",
    "devyniasdešimtus",
    "devyniasdešimtyje",
    "devyniasdešimtys",
    "devyniasdešimtyse",
    "devyniasdešimtą",
    "devyniasdešimtį",
    "devyniasdešimtų",
    "devyniasdešimčia",
    "devyniasdešimčiai",
    "devyniasdešimčių",
    "devyniems",
    "devyniolika",
    "devyniolikai",
    "devyniolikoje",
    "devyniolikos",
    "devyniolikta",
    "devynioliktai",
    "devynioliktais",
    "devynioliktam",
    "devynioliktame",
    "devynioliktas",
    "devyniolikti",
    "devynioliktiems",
    "devyniolikto",
    "devynioliktoje",
    "devynioliktomis",
    "devynioliktoms",
    "devynioliktos",
    "devynioliktose",
    "devynioliktu",
    "devynioliktuose",
    "devynioliktus",
    "devynioliktą",
    "devynioliktų",
    "devyniomis",
    "devynioms",
    "devynios",
    "devyniose",
    "devynis",
    "devyniuose",
    "devynių",
    "dešimt",
    "dešimta",
    "dešimtai",
    "dešimtais",
    "dešimtam",
    "dešimtame",
    "dešimtas",
    "dešimti",
    "dešimtiems",
    "dešimties",
    "dešimtimi",
    "dešimtimis",
    "dešimtims",
    "dešimtis",
    "dešimto",
    "dešimtoje",
    "dešimtomis",
    "dešimtoms",
    "dešimtos",
    "dešimtose",
    "dešimtu",
    "dešimtuose",
    "dešimtus",
    "dešimtyje",
    "dešimtys",
    "dešimtyse",
    "dešimtą",
    "dešimtį",
    "dešimtų",
    "dešimčia",
    "dešimčiai",
    "dešimčių",
    "du",
    "dutūkstanta",
    "dutūkstantai",
    "dutūkstantais",
    "dutūkstantam",
    "dutūkstantame",
    "dutūkstantas",
    "dutūkstanti",
    "dutūkstantiems",
    "dutūkstanto",
    "dutūkstantoje",
    "dutūkstantomis",
    "dutūkstantoms",
    "dutūkstantos",
    "dutūkstantose",
    "dutūkstantu",
    "dutūkstantuose",
    "dutūkstantus",
    "dutūkstantą",
    "dutūkstantų",
    "dušimta",
    "dušimtai",
    "dušimtais",
    "dušimtam",
    "dušimtame",
    "dušimtas",
    "dušimti",
    "dušimtiems",
    "dušimto",
    "dušimtoje",
    "dušimtomis",
    "dušimtoms",
    "dušimtos",
    "dušimtose",
    "dušimtu",
    "dušimtuose",
    "dušimtus",
    "dušimtą",
    "dušimtų",
    "dvejais",
    "dvejas",
    "dvejetai",
    "dvejetais",
    "dvejetams",
    "dvejetas",
    "dvejete",
    "dvejeto",
    "dvejetu",
    "dvejetui",
    "dvejetuose",
    "dvejetus",
    "dvejetą",
    "dvejetų",
    "dveji",
    "dvejiems",
    "dvejomis",
    "dvejoms",
    "dvejos",
    "dvejose",
    "dvejuose",
    "dvejus",
    "dvejų",
    "dvi",
    "dvidešimt",
    "dvidešimta",
    "dvidešimtai",
    "dvidešimtais",
    "dvidešimtam",
    "dvidešimtame",
    "dvidešimtas",
    "dvidešimti",
    "dvidešimtiems",
    "dvidešimties",
    "dvidešimtimi",
    "dvidešimtimis",
    "dvidešimtims",
    "dvidešimtis",
    "dvidešimto",
    "dvidešimtoje",
    "dvidešimtomis",
    "dvidešimtoms",
    "dvidešimtos",
    "dvidešimtose",
    "dvidešimtu",
    "dvidešimtuose",
    "dvidešimtus",
    "dvidešimtyje",
    "dvidešimtys",
    "dvidešimtyse",
    "dvidešimtą",
    "dvidešimtį",
    "dvidešimtų",
    "dvidešimčia",
    "dvidešimčiai",
    "dvidešimčių",
    "dviejose",
    "dviejuose",
    "dviejų",
    "dviem",
    "dvylika",
    "dvylikai",
    "dvylikoje",
    "dvylikos",
    "dvylikta",
    "dvyliktai",
    "dvyliktais",
    "dvyliktam",
    "dvyliktame",
    "dvyliktas",
    "dvylikti",
    "dvyliktiems",
    "dvylikto",
    "dvyliktoje",
    "dvyliktomis",
    "dvyliktoms",
    "dvyliktos",
    "dvyliktose",
    "dvyliktu",
    "dvyliktuose",
    "dvyliktus",
    "dvyliktą",
    "dvyliktų",
    "keturi",
    "keturiais",
    "keturias",
    "keturiasdešimt",
    "keturiasdešimta",
    "keturiasdešimtai",
    "keturiasdešimtais",
    "keturiasdešimtam",
    "keturiasdešimtame",
    "keturiasdešimtas",
    "keturiasdešimti",
    "keturiasdešimtiems",
    "keturiasdešimties",
    "keturiasdešimtimi",
    "keturiasdešimtimis",
    "keturiasdešimtims",
    "keturiasdešimtis",
    "keturiasdešimto",
    "keturiasdešimtoje",
    "keturiasdešimtomis",
    "keturiasdešimtoms",
    "keturiasdešimtos",
    "keturiasdešimtose",
    "keturiasdešimtu",
    "keturiasdešimtuose",
    "keturiasdešimtus",
    "keturiasdešimtyje",
    "keturiasdešimtys",
    "keturiasdešimtyse",
    "keturiasdešimtą",
    "keturiasdešimtį",
    "keturiasdešimtų",
    "keturiasdešimčia",
    "keturiasdešimčiai",
    "keturiasdešimčių",
    "keturiems",
    "keturiolika",
    "keturiolikai",
    "keturiolikoje",
    "keturiolikos",
    "keturiolikta",
    "keturioliktai",
    "keturioliktais",
    "keturioliktam",
    "keturioliktame",
    "keturioliktas",
    "keturiolikti",
    "keturioliktiems",
    "keturiolikto",
    "keturioliktoje",
    "keturioliktomis",
    "keturioliktoms",
    "keturioliktos",
    "keturioliktose",
    "keturioliktu",
    "keturioliktuose",
    "keturioliktus",
    "keturioliktą",
    "keturioliktų",
    "keturiomis",
    "keturioms",
    "keturios",
    "keturiose",
    "keturis",
    "keturiuose",
    "keturių",
    "ketveri",
    "ketveriais",
    "ketverias",
    "ketveriems",
    "ketveriomis",
    "ketverioms",
    "ketverios",
    "ketveriose",
    "ketveriuose",
    "ketverius",
    "ketverių",
    "ketvertai",
    "ketvertais",
    "ketvertams",
    "ketvertas",
    "ketverte",
    "ketverto",
    "ketvertu",
    "ketvertui",
    "ketvertuose",
    "ketvertus",
    "ketvertą",
    "ketvertų",
    "ketvirta",
    "ketvirtai",
    "ketvirtais",
    "ketvirtam",
    "ketvirtame",
    "ketvirtas",
    "ketvirti",
    "ketvirtiems",
    "ketvirto",
    "ketvirtoje",
    "ketvirtomis",
    "ketvirtoms",
    "ketvirtos",
    "ketvirtose",
    "ketvirtu",
    "ketvirtuose",
    "ketvirtus",
    "ketvirtą",
    "ketvirtų",
    "milijardai",
    "milijardais",
    "milijardams",
    "milijardas",
    "milijarde",
    "milijardo",
    "milijardu",
    "milijardui",
    "milijarduose",
    "milijardus",
    "milijardą",
    "milijardų",
    "milijonai",
    "milijonais",
    "milijonams",
    "milijonas",
    "milijone",
    "milijono",
    "milijonu",
    "milijonui",
    "milijonuose",
    "milijonus",
    "milijoną",
    "milijonų",
    "penkeri",
    "penkeriais",
    "penkerias",
    "penkeriems",
    "penkeriomis",
    "penkerioms",
    "penkerios",
    "penkeriose",
    "penkeriuose",
    "penkerius",
    "penkerių",
    "penketai",
    "penketais",
    "penketams",
    "penketas",
    "penkete",
    "penketo",
    "penketu",
    "penketui",
    "penketuose",
    "penketus",
    "penketą",
    "penketų",
    "penki",
    "penkiais",
    "penkias",
    "penkiasdešimt",
    "penkiasdešimta",
    "penkiasdešimtai",
    "penkiasdešimtais",
    "penkiasdešimtam",
    "penkiasdešimtame",
    "penkiasdešimtas",
    "penkiasdešimti",
    "penkiasdešimtiems",
    "penkiasdešimties",
    "penkiasdešimtimi",
    "penkiasdešimtimis",
    "penkiasdešimtims",
    "penkiasdešimtis",
    "penkiasdešimto",
    "penkiasdešimtoje",
    "penkiasdešimtomis",
    "penkiasdešimtoms",
    "penkiasdešimtos",
    "penkiasdešimtose",
    "penkiasdešimtu",
    "penkiasdešimtuose",
    "penkiasdešimtus",
    "penkiasdešimtyje",
    "penkiasdešimtys",
    "penkiasdešimtyse",
    "penkiasdešimtą",
    "penkiasdešimtį",
    "penkiasdešimtų",
    "penkiasdešimčia",
    "penkiasdešimčiai",
    "penkiasdešimčių",
    "penkiems",
    "penkiolika",
    "penkiolikai",
    "penkiolikoje",
    "penkiolikos",
    "penkiolikta",
    "penkioliktai",
    "penkioliktais",
    "penkioliktam",
    "penkioliktame",
    "penkioliktas",
    "penkiolikti",
    "penkioliktiems",
    "penkiolikto",
    "penkioliktoje",
    "penkioliktomis",
    "penkioliktoms",
    "penkioliktos",
    "penkioliktose",
    "penkioliktu",
    "penkioliktuose",
    "penkioliktus",
    "penkioliktą",
    "penkioliktų",
    "penkiomis",
    "penkioms",
    "penkios",
    "penkiose",
    "penkis",
    "penkiuose",
    "penkišimta",
    "penkišimtai",
    "penkišimtais",
    "penkišimtam",
    "penkišimtame",
    "penkišimtas",
    "penkišimti",
    "penkišimtiems",
    "penkišimto",
    "penkišimtoje",
    "penkišimtomis",
    "penkišimtoms",
    "penkišimtos",
    "penkišimtose",
    "penkišimtu",
    "penkišimtuose",
    "penkišimtus",
    "penkišimtą",
    "penkišimtų",
    "penkių",
    "penkta",
    "penktai",
    "penktais",
    "penktam",
    "penktame",
    "penktas",
    "penkti",
    "penktiems",
    "penkto",
    "penktoje",
    "penktomis",
    "penktoms",
    "penktos",
    "penktose",
    "penktu",
    "penktuose",
    "penktus",
    "penktą",
    "penktų",
    "pirma",
    "pirmai",
    "pirmais",
    "pirmaisiais",
    "pirmajai",
    "pirmajam",
    "pirmajame",
    "pirmam",
    "pirmame",
    "pirmas",
    "pirmasis",
    "pirmi",
    "pirmieji",
    "pirmiems",
    "pirmiesiems",
    "pirmo",
    "pirmoje",
    "pirmoji",
    "pirmojo",
    "pirmojoje",
    "pirmomis",
    "pirmoms",
    "pirmos",
    "pirmose",
    "pirmosiomis",
    "pirmosioms",
    "pirmosios",
    "pirmosiose",
    "pirmu",
    "pirmuoju",
    "pirmuose",
    "pirmuosiuose",
    "pirmuosius",
    "pirmus",
    "pirmą",
    "pirmąja",
    "pirmąją",
    "pirmąjį",
    "pirmąsias",
    "pirmų",
    "pirmųjų",
    "pusantro",
    "pusantros",
    "pusantrų",
    "pusdevinto",
    "pusdevintos",
    "pusdevintų",
    "pusdešimto",
    "pusdešimtos",
    "pusdešimtų",
    "pusketvirto",
    "pusketvirtos",
    "pusketvirtų",
    "puspenkto",
    "puspenktos",
    "puspenktų",
    "pusseptinto",
    "pusseptintos",
    "pusseptintų",
    "pustrečio",
    "pustrečios",
    "pustrečių",
    "pusšešto",
    "pusšeštos",
    "pusšeštų",
    "septinta",
    "septintai",
    "septintais",
    "septintam",
    "septintame",
    "septintas",
    "septinti",
    "septintiems",
    "septinto",
    "septintoje",
    "septintomis",
    "septintoms",
    "septintos",
    "septintose",
    "septintu",
    "septintuose",
    "septintus",
    "septintą",
    "septintų",
    "septyneri",
    "septyneriais",
    "septynerias",
    "septyneriems",
    "septyneriomis",
    "septynerioms",
    "septynerios",
    "septyneriose",
    "septyneriuose",
    "septynerius",
    "septynerių",
    "septynetas",
    "septynete",
    "septyneto",
    "septynetu",
    "septynetui",
    "septynetą",
    "septyni",
    "septyniais",
    "septynias",
    "septyniasdešimt",
    "septyniasdešimta",
    "septyniasdešimtai",
    "septyniasdešimtais",
    "septyniasdešimtam",
    "septyniasdešimtame",
    "septyniasdešimtas",
    "septyniasdešimti",
    "septyniasdešimtiems",
    "septyniasdešimties",
    "septyniasdešimtimi",
    "septyniasdešimtimis",
    "septyniasdešimtims",
    "septyniasdešimtis",
    "septyniasdešimto",
    "septyniasdešimtoje",
    "septyniasdešimtomis",
    "septyniasdešimtoms",
    "septyniasdešimtos",
    "septyniasdešimtose",
    "septyniasdešimtu",
    "septyniasdešimtuose",
    "septyniasdešimtus",
    "septyniasdešimtyje",
    "septyniasdešimtys",
    "septyniasdešimtyse",
    "septyniasdešimtą",
    "septyniasdešimtį",
    "septyniasdešimtų",
    "septyniasdešimčia",
    "septyniasdešimčiai",
    "septyniasdešimčių",
    "septyniems",
    "septyniolika",
    "septyniolikai",
    "septyniolikoje",
    "septyniolikos",
    "septyniolikta",
    "septynioliktai",
    "septynioliktais",
    "septynioliktam",
    "septynioliktame",
    "septynioliktas",
    "septyniolikti",
    "septynioliktiems",
    "septyniolikto",
    "septynioliktoje",
    "septynioliktomis",
    "septynioliktoms",
    "septynioliktos",
    "septynioliktose",
    "septynioliktu",
    "septynioliktuose",
    "septynioliktus",
    "septynioliktą",
    "septynioliktų",
    "septyniomis",
    "septynioms",
    "septynios",
    "septyniose",
    "septynis",
    "septyniuose",
    "septynių",
    "skaitvardi",
    "skaitvardis",
    "skaitvardyje",
    "skaitvardį",
    "skaitvardžiai",
    "skaitvardžiais",
    "skaitvardžiams",
    "skaitvardžio",
    "skaitvardžiu",
    "skaitvardžiui",
    "skaitvardžiuose",
    "skaitvardžius",
    "skaitvardžių",
    "trejais",
    "trejas",
    "trejetai",
    "trejetais",
    "trejetams",
    "trejetas",
    "trejete",
    "trejeto",
    "trejetu",
    "trejetui",
    "trejetuose",
    "trejetus",
    "trejetą",
    "trejetų",
    "treji",
    "trejiems",
    "trejomis",
    "trejoms",
    "trejos",
    "trejose",
    "trejuose",
    "trejus",
    "trejų",
    "treti",
    "tretiems",
    "trečia",
    "trečiai",
    "trečiais",
    "trečiam",
    "trečiame",
    "trečias",
    "trečio",
    "trečioje",
    "trečiomis",
    "trečioms",
    "trečios",
    "trečiose",
    "trečiu",
    "trečiuose",
    "trečius",
    "trečią",
    "trečių",
    "trijose",
    "trijuose",
    "trijų",
    "trilijonai",
    "trilijonais",
    "trilijonams",
    "trilijonas",
    "trilijone",
    "trilijono",
    "trilijonu",
    "trilijonui",
    "trilijonuose",
    "trilijonus",
    "trilijoną",
    "trilijonų",
    "trimis",
    "trims",
    "tris",
    "trisdešimt",
    "trisdešimta",
    "trisdešimtai",
    "trisdešimtais",
    "trisdešimtam",
    "trisdešimtame",
    "trisdešimtas",
    "trisdešimti",
    "trisdešimtiems",
    "trisdešimties",
    "trisdešimtimi",
    "trisdešimtimis",
    "trisdešimtims",
    "trisdešimtis",
    "trisdešimto",
    "trisdešimtoje",
    "trisdešimtomis",
    "trisdešimtoms",
    "trisdešimtos",
    "trisdešimtose",
    "trisdešimtu",
    "trisdešimtuose",
    "trisdešimtus",
    "trisdešimtyje",
    "trisdešimtys",
    "trisdešimtyse",
    "trisdešimtą",
    "trisdešimtį",
    "trisdešimtų",
    "trisdešimčia",
    "trisdešimčiai",
    "trisdešimčių",
    "trylika",
    "trylikai",
    "trylikoje",
    "trylikos",
    "trylikta",
    "tryliktai",
    "tryliktais",
    "tryliktam",
    "tryliktame",
    "tryliktas",
    "trylikti",
    "tryliktiems",
    "trylikto",
    "tryliktoje",
    "tryliktomis",
    "tryliktoms",
    "tryliktos",
    "tryliktose",
    "tryliktu",
    "tryliktuose",
    "tryliktus",
    "tryliktą",
    "tryliktų",
    "trys",
    "tūkstanta",
    "tūkstantai",
    "tūkstantais",
    "tūkstantam",
    "tūkstantame",
    "tūkstantas",
    "tūkstanti",
    "tūkstantiems",
    "tūkstantis",
    "tūkstanto",
    "tūkstantoje",
    "tūkstantomis",
    "tūkstantoms",
    "tūkstantos",
    "tūkstantose",
    "tūkstantu",
    "tūkstantuose",
    "tūkstantus",
    "tūkstantyje",
    "tūkstantą",
    "tūkstantį",
    "tūkstantų",
    "tūkstančiai",
    "tūkstančiais",
    "tūkstančiams",
    "tūkstančio",
    "tūkstančiu",
    "tūkstančiui",
    "tūkstančiuose",
    "tūkstančius",
    "tūkstančių",
    "vienam",
    "viename",
    "vienas",
    "vieno",
    "vienu",
    "vienuolika",
    "vienuolikai",
    "vienuolikoje",
    "vienuolikos",
    "vienuolikta",
    "vienuoliktais",
    "vienuoliktam",
    "vienuoliktame",
    "vienuoliktas",
    "vienuolikti",
    "vienuoliktiems",
    "vienuolikto",
    "vienuoliktoje",
    "vienuoliktomis",
    "vienuoliktoms",
    "vienuoliktos",
    "vienuoliktose",
    "vienuoliktu",
    "vienuoliktuose",
    "vienuoliktus",
    "vienuoliktą",
    "vienuoliktų",
    "vienuoliką",
    "vieną",
    "šešeri",
    "šešeriais",
    "šešerias",
    "šešeriems",
    "šešeriomis",
    "šešerioms",
    "šešerios",
    "šešeriose",
    "šešeriuose",
    "šešerius",
    "šešerių",
    "šešetai",
    "šešetais",
    "šešetams",
    "šešetas",
    "šešete",
    "šešeto",
    "šešetu",
    "šešetui",
    "šešetuose",
    "šešetus",
    "šešetą",
    "šešetų",
    "šeši",
    "šešiais",
    "šešias",
    "šešiasdešimt",
    "šešiasdešimta",
    "šešiasdešimtai",
    "šešiasdešimtais",
    "šešiasdešimtam",
    "šešiasdešimtame",
    "šešiasdešimtas",
    "šešiasdešimti",
    "šešiasdešimtiems",
    "šešiasdešimties",
    "šešiasdešimtimi",
    "šešiasdešimtimis",
    "šešiasdešimtims",
    "šešiasdešimtis",
    "šešiasdešimto",
    "šešiasdešimtoje",
    "šešiasdešimtomis",
    "šešiasdešimtoms",
    "šešiasdešimtos",
    "šešiasdešimtose",
    "šešiasdešimtu",
    "šešiasdešimtuose",
    "šešiasdešimtus",
    "šešiasdešimtyje",
    "šešiasdešimtys",
    "šešiasdešimtyse",
    "šešiasdešimtą",
    "šešiasdešimtį",
    "šešiasdešimtų",
    "šešiasdešimčia",
    "šešiasdešimčiai",
    "šešiasdešimčių",
    "šešiems",
    "šešiolika",
    "šešiolikai",
    "šešiolikoje",
    "šešiolikos",
    "šešiolikta",
    "šešioliktai",
    "šešioliktais",
    "šešioliktam",
    "šešioliktame",
    "šešioliktas",
    "šešiolikti",
    "šešioliktiems",
    "šešiolikto",
    "šešioliktoje",
    "šešioliktomis",
    "šešioliktoms",
    "šešioliktos",
    "šešioliktose",
    "šešioliktu",
    "šešioliktuose",
    "šešioliktus",
    "šešioliktą",
    "šešioliktų",
    "šešiomis",
    "šešioms",
    "šešios",
    "šešiose",
    "šešis",
    "šešiuose",
    "šešių",
    "šešta",
    "šeštai",
    "šeštais",
    "šeštam",
    "šeštame",
    "šeštas",
    "šešti",
    "šeštiems",
    "šešto",
    "šeštoje",
    "šeštomis",
    "šeštoms",
    "šeštos",
    "šeštose",
    "šeštu",
    "šeštuose",
    "šeštus",
    "šeštą",
    "šeštų",
    "šimtai",
    "šimtais",
    "šimtams",
    "šimtas",
    "šimte",
    "šimto",
    "šimtu",
    "šimtui",
    "šimtuose",
    "šimtus",
    "šimtą",
    "šimtų",
}


def like_num(text):
    text = text.replace(",", "").replace(".", "")
    if text.isdigit():
        return True
    if text.count("/") == 1:
        num, denom = text.split("/")
        if num.isdigit() and denom.isdigit():
            return True
    if text.lower() in _num_words:
        return True
    return False


LEX_ATTRS = {LIKE_NUM: like_num}
