{"directory": ".", "scan_time": "2025-07-15T06:46:56.885440", "total_files": 19474, "audio_files_count": 22, "audio_files": [".venv/lib/python3.11/site-packages/scipy/io/tests/data/test-8000Hz-le-3ch-5S-45bit.wav", ".venv/lib/python3.11/site-packages/scipy/io/tests/data/test-8000Hz-le-1ch-1byte-ulaw.wav", ".venv/lib/python3.11/site-packages/scipy/io/tests/data/test-8000Hz-le-3ch-5S-36bit.wav", ".venv/lib/python3.11/site-packages/scipy/io/tests/data/test-8000Hz-le-3ch-5S-24bit.wav", ".venv/lib/python3.11/site-packages/scipy/io/tests/data/test-8000Hz-le-3ch-5S-24bit-rf64.wav", ".venv/lib/python3.11/site-packages/scipy/io/tests/data/test-44100Hz-2ch-32bit-float-le.wav", ".venv/lib/python3.11/site-packages/scipy/io/tests/data/test-44100Hz-le-1ch-4bytes.wav", ".venv/lib/python3.11/site-packages/scipy/io/tests/data/test-44100Hz-be-1ch-4bytes.wav", ".venv/lib/python3.11/site-packages/scipy/io/tests/data/test-48000Hz-2ch-64bit-float-le-wavex.wav", ".venv/lib/python3.11/site-packages/scipy/io/tests/data/test-8000Hz-le-3ch-5S-53bit.wav", ".venv/lib/python3.11/site-packages/scipy/io/tests/data/test-8000Hz-le-5ch-9S-5bit.wav", ".venv/lib/python3.11/site-packages/scipy/io/tests/data/test-8000Hz-le-4ch-9S-12bit.wav", ".venv/lib/python3.11/site-packages/scipy/io/tests/data/test-44100Hz-le-1ch-4bytes-early-eof.wav", ".venv/lib/python3.11/site-packages/scipy/io/tests/data/test-8000Hz-be-3ch-5S-24bit.wav", ".venv/lib/python3.11/site-packages/scipy/io/tests/data/test-44100Hz-le-1ch-4bytes-rf64.wav", ".venv/lib/python3.11/site-packages/scipy/io/tests/data/test-44100Hz-le-1ch-4bytes-incomplete-chunk.wav", ".venv/lib/python3.11/site-packages/scipy/io/tests/data/test-8000Hz-le-2ch-1byteu.wav", ".venv/lib/python3.11/site-packages/scipy/io/tests/data/test-1234Hz-le-1ch-10S-20bit-extra.wav", ".venv/lib/python3.11/site-packages/scipy/io/tests/data/test-8000Hz-le-3ch-5S-64bit.wav", ".venv/lib/python3.11/site-packages/scipy/io/tests/data/test-44100Hz-2ch-32bit-float-be.wav", ".venv/lib/python3.11/site-packages/scipy/io/tests/data/test-44100Hz-le-1ch-4bytes-early-eof-no-data.wav", ".venv/lib/python3.11/site-packages/scipy/io/tests/data/test-8000Hz-le-3ch-5S-24bit-inconsistent.wav"], "errors_count": 0, "scan_duration": 0.360157, "directories_scanned": 2354}